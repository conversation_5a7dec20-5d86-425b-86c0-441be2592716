// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import '../models/dashboard_models.dart';
// import '../services/dashboard_data_service.dart';
// import '../controllers/dashboard_controller.dart';

// /// مساعد اختبار نظام لوحة التحكم الشامل
// class DashboardTestHelper {
//   static const String _tag = 'DashboardTest';

//   /// اختبار تحميل البيانات الأساسية
//   static Future<bool> testBasicDataLoading() async {
//     try {
//       debugPrint('[$_tag] بدء اختبار تحميل البيانات الأساسية...');

//       final dataService = DashboardDataService();

//       // اختبار جلب إحصائيات المهام
//       final taskStats = await dataService.getTaskStatistics();
//       debugPrint('[$_tag] إحصائيات المهام: ${taskStats.totalTasks} مهمة');

//       // اختبار جلب إحصائيات المستخدمين
//       final userStats = await dataService.getUserStatistics();
//       debugPrint('[$_tag] إحصائيات المستخدمين: ${userStats.totalUsers} مستخدم');

//       // اختبار جلب بيانات المخططات
//       final tasksByStatus = await dataService.getTasksByStatus();
//       debugPrint('[$_tag] المهام حسب الحالة: ${tasksByStatus.length} حالة');

//       final tasksByPriority = await dataService.getTasksByPriority();
//       debugPrint(
//           '[$_tag] المهام حسب الأولوية: ${tasksByPriority.length} أولوية');

//       debugPrint('[$_tag] ✅ اختبار تحميل البيانات الأساسية نجح');
//       return true;
//     } catch (e) {
//       debugPrint('[$_tag] ❌ فشل اختبار تحميل البيانات الأساسية: $e');
//       return false;
//     }
//   }

//   /// اختبار وظائف المتحكم
//   static Future<bool> testControllerFunctions() async {
//     try {
//       debugPrint('[$_tag] بدء اختبار وظائف المتحكم...');

//       // تهيئة المتحكم
//       final controller = DashboardController();

//       // اختبار تحميل البيانات
//       await controller.loadDashboardData();

//       if (controller.error.isNotEmpty) {
//         debugPrint('[$_tag] خطأ في المتحكم: ${controller.error}');
//         return false;
//       }

//       // اختبار تطبيق المرشحات
//       final testFilter = DataFilter(
//         id: 'test_filter',
//         name: 'مرشح اختبار',
//         type: FilterType.toggle,
//         value: true,
//         isActive: true,
//       );

//       controller.applyFilter(testFilter);

//       if (!controller.activeFilters.containsKey('test_filter')) {
//         debugPrint('[$_tag] فشل في تطبيق المرشح');
//         return false;
//       }

//       // اختبار إزالة المرشح
//       controller.removeFilter('test_filter');

//       if (controller.activeFilters.containsKey('test_filter')) {
//         debugPrint('[$_tag] فشل في إزالة المرشح');
//         return false;
//       }

//       // اختبار تغيير نوع المخطط
//       if (controller.chartCards.isNotEmpty) {
//         final firstCard = controller.chartCards.first;
//         final originalType = firstCard.chartType;
//         final newType = firstCard.supportedChartTypes.firstWhere(
//             (type) => type != originalType,
//             orElse: () => originalType);

//         controller.changeChartType(firstCard.id, newType);

//         final updatedCard =
//             controller.chartCards.firstWhere((card) => card.id == firstCard.id);
//         if (updatedCard.chartType != newType) {
//           debugPrint('[$_tag] فشل في تغيير نوع المخطط');
//           return false;
//         }
//       }

//       debugPrint('[$_tag] ✅ اختبار وظائف المتحكم نجح');
//       return true;
//     } catch (e) {
//       debugPrint('[$_tag] ❌ فشل اختبار وظائف المتحكم: $e');
//       return false;
//     }
//   }

//   /// اختبار إنشاء البيانات الوهمية
//   static List<ChartDataPoint> generateMockData(String category, int count) {
//     final mockData = <ChartDataPoint>[];

//     switch (category) {
//       case 'status':
//         mockData.addAll([
//           const ChartDataPoint(
//               label: 'قيد الانتظار', value: 15, color: Colors.orange),
//           const ChartDataPoint(
//               label: 'قيد التنفيذ', value: 25, color: Colors.blue),
//           const ChartDataPoint(label: 'مكتملة', value: 40, color: Colors.green),
//           const ChartDataPoint(label: 'ملغية', value: 5, color: Colors.red),
//         ]);
//         break;

//       case 'priority':
//         mockData.addAll([
//           const ChartDataPoint(label: 'منخفضة', value: 20, color: Colors.green),
//           const ChartDataPoint(
//               label: 'متوسطة', value: 35, color: Colors.orange),
//           const ChartDataPoint(label: 'عالية', value: 30, color: Colors.red),
//           const ChartDataPoint(label: 'عاجلة', value: 10, color: Colors.purple),
//         ]);
//         break;

//       case 'departments':
//         mockData.addAll([
//           const ChartDataPoint(label: 'تقنية المعلومات', value: 45),
//           const ChartDataPoint(label: 'الموارد البشرية', value: 20),
//           const ChartDataPoint(label: 'المالية', value: 15),
//           const ChartDataPoint(label: 'التسويق', value: 25),
//         ]);
//         break;

//       default:
//         for (int i = 0; i < count; i++) {
//           mockData.add(ChartDataPoint(
//             label: 'عنصر ${i + 1}',
//             value: (i + 1) * 10.0,
//           ));
//         }
//     }

//     return mockData.take(count).toList();
//   }

//   /// اختبار إنشاء بطاقة مخطط
//   static ChartCardModel createTestChartCard({
//     required String id,
//     required String title,
//     required ChartType chartType,
//     String category = 'status',
//     int dataCount = 4,
//   }) {
//     return ChartCardModel(
//       id: id,
//       title: title,
//       description: 'بطاقة اختبار للمخطط $title',
//       chartType: chartType,
//       data: generateMockData(category, dataCount),
//       supportedChartTypes: [
//         ChartType.pie,
//         ChartType.column,
//         ChartType.bar,
//         ChartType.doughnut,
//       ],
//       size: const Size(400, 300),
//       isResizable: true,
//       dataSource: 'test_$category',
//     );
//   }

//   /// اختبار شامل للنظام
//   static Future<Map<String, bool>> runComprehensiveTest() async {
//     final results = <String, bool>{};

//     debugPrint('[$_tag] 🚀 بدء الاختبار الشامل لنظام لوحة التحكم...');

//     // اختبار تحميل البيانات
//     results['data_loading'] = await testBasicDataLoading();

//     // اختبار وظائف المتحكم
//     results['controller_functions'] = await testControllerFunctions();

//     // اختبار إنشاء البيانات الوهمية
//     try {
//       final mockData = generateMockData('status', 4);
//       results['mock_data_generation'] = mockData.isNotEmpty;
//       debugPrint('[$_tag] ✅ اختبار إنشاء البيانات الوهمية نجح');
//     } catch (e) {
//       results['mock_data_generation'] = false;
//       debugPrint('[$_tag] ❌ فشل اختبار إنشاء البيانات الوهمية: $e');
//     }

//     // اختبار إنشاء بطاقة المخطط
//     try {
//       final testCard = createTestChartCard(
//         id: 'test_card',
//         title: 'بطاقة اختبار',
//         chartType: ChartType.pie,
//       );
//       results['chart_card_creation'] = testCard.data.isNotEmpty;
//       debugPrint('[$_tag] ✅ اختبار إنشاء بطاقة المخطط نجح');
//     } catch (e) {
//       results['chart_card_creation'] = false;
//       debugPrint('[$_tag] ❌ فشل اختبار إنشاء بطاقة المخطط: $e');
//     }

//     // عرض النتائج النهائية
//     final passedTests = results.values.where((result) => result).length;
//     final totalTests = results.length;

//     debugPrint('[$_tag] 📊 نتائج الاختبار الشامل:');
//     debugPrint('[$_tag] ✅ نجح: $passedTests من $totalTests اختبار');

//     results.forEach((testName, result) {
//       final status = result ? '✅' : '❌';
//       debugPrint('[$_tag] $status $testName');
//     });

//     if (passedTests == totalTests) {
//       debugPrint('[$_tag] 🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.');
//     } else {
//       debugPrint('[$_tag] ⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.');
//     }

//     return results;
//   }

//   /// اختبار الأداء
//   static Future<Map<String, Duration>> performanceTest() async {
//     final results = <String, Duration>{};

//     debugPrint('[$_tag] ⏱️ بدء اختبار الأداء...');

//     // اختبار سرعة تحميل البيانات
//     final dataLoadingStart = DateTime.now();
//     await testBasicDataLoading();
//     results['data_loading'] = DateTime.now().difference(dataLoadingStart);

//     // اختبار سرعة إنشاء المخططات
//     final chartCreationStart = DateTime.now();
//     for (int i = 0; i < 10; i++) {
//       createTestChartCard(
//         id: 'perf_test_$i',
//         title: 'اختبار أداء $i',
//         chartType: ChartType.values[i % ChartType.values.length],
//       );
//     }
//     results['chart_creation'] = DateTime.now().difference(chartCreationStart);

//     // اختبار سرعة تطبيق المرشحات
//     final filterStart = DateTime.now();
//     final controller = DashboardController();
//     for (int i = 0; i < 5; i++) {
//       final filter = DataFilter(
//         id: 'perf_filter_$i',
//         name: 'مرشح أداء $i',
//         type: FilterType.toggle,
//         value: true,
//       );
//       controller.applyFilter(filter);
//     }
//     results['filter_application'] = DateTime.now().difference(filterStart);

//     debugPrint('[$_tag] 📈 نتائج اختبار الأداء:');
//     results.forEach((operation, duration) {
//       debugPrint('[$_tag] $operation: ${duration.inMilliseconds}ms');
//     });

//     return results;
//   }

//   /// تنظيف بيانات الاختبار
//   static void cleanup() {
//     debugPrint('[$_tag] 🧹 تنظيف بيانات الاختبار...');

//     // إزالة المتحكمات المؤقتة
//     if (Get.isRegistered<DashboardController>()) {
//       Get.delete<DashboardController>();
//     }

//     debugPrint('[$_tag] ✅ تم تنظيف بيانات الاختبار');
//   }

//   /// عرض معلومات النظام
//   static void displaySystemInfo() {
//     debugPrint('[$_tag] 📋 معلومات نظام لوحة التحكم:');
//     debugPrint('[$_tag] - الإصدار: 1.0.0');
//     debugPrint('[$_tag] - أنواع المخططات المدعومة: ${ChartType.values.length}');
//     debugPrint(
//         '[$_tag] - أنواع المرشحات المدعومة: ${FilterType.values.length}');
//     debugPrint('[$_tag] - مكتبة المخططات: Syncfusion Flutter Charts');
//     debugPrint('[$_tag] - إدارة الحالة: GetX');
//   }
// }
