import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/dashboard_models.dart';
import '../controllers/dashboard_controller.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';

/// ويدجت فلترة خاص بالبطاقة الواحدة
class CardFilterWidget extends StatefulWidget {
  final ChartCardModel card;
  final Function(Map<String, DataFilter>)? onFiltersChanged;

  const CardFilterWidget({
    super.key,
    required this.card,
    this.onFiltersChanged,
  });

  @override
  State<CardFilterWidget> createState() => _CardFilterWidgetState();
}

class _CardFilterWidgetState extends State<CardFilterWidget> {
  late Map<String, DataFilter> _cardFilters;
  List<DataFilter> _availableFilters = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _cardFilters = Map.from(widget.card.cardFilters);
    _loadAvailableFilters();
  }

  /// تحميل الفلاتر المتاحة من البيانات الحقيقية
  Future<void> _loadAvailableFilters() async {
    try {
      final controller = Get.find<DashboardController>();
      final filters = await controller.getAvailableFiltersForCard(widget.card.id);

      if (mounted) {
        setState(() {
          _availableFilters = filters;
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل فلاتر البطاقة: $e');
      if (mounted) {
        setState(() {
          _availableFilters = [];
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 16),
          _buildFiltersList(),
          const SizedBox(height: 16),
          _buildActionButtons(),
        ],
      ),
    );
  }

  /// بناء رأس الفلاتر
  Widget _buildHeader() {
    return Row(
      children: [
        Icon(
          Icons.filter_list,
          color: AppColors.primary,
          size: 20,
        ),
        const SizedBox(width: 8),
        Text(
          'فلاتر البطاقة',
          style: AppStyles.titleMedium.copyWith(
            color: AppColors.primary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        if (_cardFilters.isNotEmpty)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              '${_cardFilters.length} فلتر نشط',
              style: AppStyles.bodySmall.copyWith(
                color: AppColors.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
      ],
    );
  }

  /// بناء قائمة الفلاتر
  Widget _buildFiltersList() {
    if (_isLoading) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_availableFilters.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.background,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(
              Icons.info_outline,
              color: AppColors.textSecondary,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              'لا توجد فلاتر متاحة لهذه البطاقة',
              style: AppStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      children: _availableFilters.map((filter) {
        final isActive = _cardFilters.containsKey(filter.id);
        return _buildFilterItem(filter, isActive);
      }).toList(),
    );
  }

  /// بناء عنصر فلتر واحد
  Widget _buildFilterItem(DataFilter filter, bool isActive) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isActive ? AppColors.primary.withValues(alpha: 0.1) : AppColors.background,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isActive ? AppColors.primary : AppColors.border,
          width: isActive ? 2 : 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Checkbox(
                value: isActive,
                onChanged: (value) {
                  setState(() {
                    if (value == true) {
                      // إضافة الفلتر مع تفعيله
                      _cardFilters[filter.id] = filter.copyWith(isActive: true);
                    } else {
                      // إزالة الفلتر أو إلغاء تفعيله
                      _cardFilters.remove(filter.id);
                    }
                  });
                },
                activeColor: AppColors.primary,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  filter.name,
                  style: AppStyles.bodyMedium.copyWith(
                    fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
                    color: isActive ? AppColors.primary : AppColors.textPrimary,
                  ),
                ),
              ),
            ],
          ),
          if (isActive && filter.type != FilterType.toggle)
            Padding(
              padding: const EdgeInsets.only(left: 40, top: 8),
              child: _buildFilterValueSelector(filter),
            ),
        ],
      ),
    );
  }

  /// بناء محدد قيمة الفلتر
  Widget _buildFilterValueSelector(DataFilter filter) {
    switch (filter.type) {
      case FilterType.dropdown:
        return _buildDropdownSelector(filter);
      case FilterType.multiSelect:
        return _buildMultiSelectSelector(filter);
      case FilterType.dateRange:
        return _buildDateRangeSelector(filter);
      case FilterType.slider:
        return _buildSliderSelector(filter);
      default:
        return const SizedBox.shrink();
    }
  }

  /// بناء محدد القائمة المنسدلة
  Widget _buildDropdownSelector(DataFilter filter) {
    final currentFilter = _cardFilters[filter.id] ?? filter;

    return DropdownButtonFormField<String>(
      value: currentFilter.value as String?,
      decoration: InputDecoration(
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
        ),
      ),
      items: filter.options?.map((option) {
        return DropdownMenuItem<String>(
          value: option.toString(),
          child: Text(option.toString()),
        );
      }).toList(),
      onChanged: (value) {
        setState(() {
          _cardFilters[filter.id] = currentFilter.copyWith(
            value: value,
            isActive: true,
          );
        });
      },
    );
  }

  /// بناء محدد متعدد الاختيارات
  Widget _buildMultiSelectSelector(DataFilter filter) {
    final currentFilter = _cardFilters[filter.id] ?? filter;
    final selectedValues = (currentFilter.value as List<dynamic>?) ?? [];

    return Wrap(
      spacing: 8,
      runSpacing: 4,
      children: filter.options?.map((option) {
        final isSelected = selectedValues.contains(option);
        return FilterChip(
          label: Text(option.toString()),
          selected: isSelected,
          onSelected: (selected) {
            setState(() {
              final newValues = List<dynamic>.from(selectedValues);
              if (selected) {
                newValues.add(option);
              } else {
                newValues.remove(option);
              }
              _cardFilters[filter.id] = currentFilter.copyWith(
                value: newValues,
                isActive: newValues.isNotEmpty,
              );
            });
          },
          selectedColor: AppColors.primary.withValues(alpha: 0.2),
          checkmarkColor: AppColors.primary,
        );
      }).toList() ?? [],
    );
  }

  /// بناء محدد نطاق التاريخ
  Widget _buildDateRangeSelector(DataFilter filter) {
    final currentFilter = _cardFilters[filter.id] ?? filter;

    return ElevatedButton.icon(
      onPressed: () async {
        final dateRange = await showDateRangePicker(
          context: context,
          firstDate: DateTime(2020),
          lastDate: DateTime.now().add(const Duration(days: 365)),
          initialDateRange: currentFilter.value as DateTimeRange?,
        );

        if (dateRange != null) {
          setState(() {
            _cardFilters[filter.id] = currentFilter.copyWith(
              value: dateRange,
              isActive: true,
            );
          });
        }
      },
      icon: const Icon(Icons.date_range, size: 16),
      label: Text(
        currentFilter.value != null
            ? 'تم اختيار النطاق'
            : 'اختر نطاق التاريخ',
        style: const TextStyle(fontSize: 12),
      ),
    );
  }

  /// بناء محدد الشريط المنزلق
  Widget _buildSliderSelector(DataFilter filter) {
    final currentFilter = _cardFilters[filter.id] ?? filter;
    final currentValue = (currentFilter.value as double?) ?? 0.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'القيمة: ${currentValue.toStringAsFixed(0)}',
          style: const TextStyle(fontSize: 12),
        ),
        Slider(
          value: currentValue,
          min: 0,
          max: 100,
          divisions: 10,
          onChanged: (value) {
            setState(() {
              _cardFilters[filter.id] = currentFilter.copyWith(
                value: value,
                isActive: true,
              );
            });
          },
        ),
      ],
    );
  }

  /// بناء أزرار الإجراءات
  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () async {
              // تطبيق الفلاتر وتحديث البطاقة
              widget.onFiltersChanged?.call(_cardFilters);

              // إغلاق النافذة
              Navigator.of(context).pop();

              // إظهار رسالة تأكيد
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم تطبيق ${_cardFilters.length} فلتر'),
                  duration: const Duration(seconds: 2),
                  backgroundColor: AppColors.success,
                ),
              );
            },
            icon: const Icon(Icons.check, size: 16),
            label: const Text('تطبيق الفلاتر'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () {
              setState(() {
                _cardFilters.clear();
              });
            },
            icon: const Icon(Icons.clear, size: 16),
            label: const Text('مسح الكل'),
          ),
        ),
      ],
    );
  }


}
