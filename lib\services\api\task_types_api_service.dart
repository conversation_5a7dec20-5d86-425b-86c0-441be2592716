import 'package:flutter/foundation.dart';
import '../../models/task_type_models.dart';
import 'api_service.dart';

/// خدمة API لأنواع المهام - متطابقة مع ASP.NET Core API
class TaskTypesApiService {
  final ApiService _apiService = ApiService();

  /// الحصول على جميع أنواع المهام
  Future<List<TaskType>> getAllTypes() async {
    try {
      final response = await _apiService.get('/api/TaskTypes');
      return _apiService.handleListResponse<TaskType>(
        response,
        (json) => TaskType.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحميل أنواع المهام: $e');
      rethrow;
    }
  }

  /// الحصول على نوع مهمة بواسطة المعرف
  Future<TaskType?> getTypeById(int id) async {
    try {
      final response = await _apiService.get('/api/TaskTypes/$id');
      return _apiService.handleResponse<TaskType>(
        response,
        (json) => TaskType.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحميل نوع المهمة $id: $e');
      return null;
    }
  }

  /// الحصول على جميع أنواع المهام (لا يوجد مفهوم نشط/غير نشط)
  Future<List<TaskType>> getActiveTypes() async {
    return getAllTypes();
  }

  /// البحث في أنواع المهام
  Future<List<TaskType>> searchTypes(String query) async {
    try {
      final response = await _apiService.get('/api/TaskTypes/search?searchTerm=$query');
      return _apiService.handleListResponse<TaskType>(
        response,
        (json) => TaskType.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في البحث عن أنواع المهام: $e');
      return [];
    }
  }

  /// إنشاء نوع مهمة جديد
  Future<TaskType> createType(TaskType taskType) async {
    try {
      final response = await _apiService.post(
        '/api/TaskTypes',
        taskType.toJson(),
      );
      return _apiService.handleResponse<TaskType>(
        response,
        (json) => TaskType.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء نوع المهمة: $e');
      rethrow;
    }
  }

  /// تحديث نوع مهمة
  Future<TaskType> updateType(int id, TaskType taskType) async {
    try {
      final response = await _apiService.put(
        '/api/TaskTypes/$id',
        taskType.toJson(),
      );

      // Backend يرجع 204 No Content عند النجاح
      if (response.statusCode == 204) {
        // إرجاع نفس البيانات المرسلة مع التحديث
        return taskType;
      }

      return _apiService.handleResponse<TaskType>(
        response,
        (json) => TaskType.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحديث نوع المهمة $id: $e');
      rethrow;
    }
  }

  /// حذف نوع مهمة
  Future<bool> deleteType(int id) async {
    try {
      final response = await _apiService.delete('/api/TaskTypes/$id');
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في حذف نوع المهمة $id: $e');
      return false;
    }
  }

  /// الحصول على النوع الافتراضي
  Future<TaskType?> getDefaultType() async {
    try {
      final response = await _apiService.get('/api/TaskTypes/default');
      return _apiService.handleResponse<TaskType>(
        response,
        (json) => TaskType.fromJson(json),
      );
    } catch (e) {
      debugPrint('خطأ في تحميل نوع المهمة الافتراضي: $e');
      return null;
    }
  }

  /// تعيين نوع كافتراضي
  Future<bool> setAsDefault(int id) async {
    try {
      final response = await _apiService.patch('/api/TaskTypes/$id/set-default', {});
      return response.statusCode >= 200 && response.statusCode < 300;
    } catch (e) {
      debugPrint('خطأ في تعيين نوع المهمة كافتراضي $id: $e');
      return false;
    }
  }

  /// تفعيل/إلغاء تفعيل نوع مهمة (غير مدعوم في قاعدة البيانات الحالية)
  Future<bool> toggleActive(int id, bool isActive) async {
    debugPrint('تفعيل/إلغاء تفعيل أنواع المهام غير مدعوم في قاعدة البيانات الحالية');
    return false;
  }
}
