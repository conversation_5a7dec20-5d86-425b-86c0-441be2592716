import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/dashboard_models.dart';
import '../services/dashboard_data_service.dart';
import '../services/dashboard_preferences_service.dart';
import '../services/color_preferences_service.dart';
import '../config/dashboard_config.dart';
import '../../controllers/task_controller.dart';
import '../../models/task_models.dart';

/// متحكم لوحة التحكم الشامل
class DashboardController extends GetxController {
  final DashboardDataService _dataService = DashboardDataService();
  final DashboardPreferencesService _preferencesService = DashboardPreferencesService.instance;

  // الاستفادة من TaskController القوي للفلترة
  TaskController get _taskController => Get.find<TaskController>();

  // حالة التحميل والأخطاء
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;
  final RxBool _isRefreshing = false.obs;

  // بيانات لوحة التحكم
  final Rx<TaskStatistics> _taskStatistics = TaskStatistics.empty().obs;
  final Rx<UserStatistics> _userStatistics = UserStatistics.empty().obs;
  final RxList<ChartCardModel> _chartCards = <ChartCardModel>[].obs;
  final Rx<DashboardSettings> _settings = const DashboardSettings().obs;
  final RxList<DashboardLayout> _layouts = <DashboardLayout>[].obs;
  final Rx<DashboardLayout?> _currentLayout = Rx<DashboardLayout?>(null);



  // Getters
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  bool get isRefreshing => _isRefreshing.value;
  TaskStatistics get taskStatistics => _taskStatistics.value;
  UserStatistics get userStatistics => _userStatistics.value;
  List<ChartCardModel> get chartCards => _chartCards;
  DashboardSettings get settings => _settings.value;
  List<DashboardLayout> get layouts => _layouts;
  DashboardLayout? get currentLayout => _currentLayout.value;


  @override
  void onInit() {
    super.onInit();
    _initializeDashboard();
  }

  /// تحميل الألوان المحفوظة لجميع البطاقات
  Future<void> loadSavedColors() async {
    try {
      for (int i = 0; i < _chartCards.length; i++) {
        final card = _chartCards[i];
        final savedColors = await ColorPreferencesService.getCardColors(card.id);

        if (savedColors.isNotEmpty) {
          // تطبيق الألوان المحفوظة على البيانات
          final updatedData = _applyColorsToData(card.data, savedColors);

          _chartCards[i] = card.copyWith(
            colorMapping: savedColors,
            data: updatedData,
          );

          debugPrint('✅ تم تحميل ألوان محفوظة للبطاقة ${card.id}: ${savedColors.length} لون');
        }
      }

      // تحميل ترتيب البطاقات المحفوظ
      await _loadSavedCardOrder();

      update();
    } catch (e) {
      debugPrint('خطأ في تحميل الألوان المحفوظة: $e');
    }
  }

  /// تحميل ترتيب البطاقات المحفوظ
  Future<void> _loadSavedCardOrder() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedOrder = prefs.getStringList('dashboard_card_order');

      if (savedOrder != null && savedOrder.isNotEmpty) {
        // إعادة ترتيب البطاقات حسب الترتيب المحفوظ
        final reorderedCards = <ChartCardModel>[];

        // إضافة البطاقات حسب الترتيب المحفوظ
        for (final cardId in savedOrder) {
          final card = _chartCards.firstWhereOrNull((c) => c.id == cardId);
          if (card != null) {
            reorderedCards.add(card);
          }
        }

        // إضافة أي بطاقات جديدة لم تكن في الترتيب المحفوظ
        for (final card in _chartCards) {
          if (!reorderedCards.any((c) => c.id == card.id)) {
            reorderedCards.add(card);
          }
        }

        _chartCards.assignAll(reorderedCards);
        debugPrint('✅ تم تحميل ترتيب البطاقات المحفوظ: ${savedOrder.length} بطاقة');
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل ترتيب البطاقات: $e');
    }
  }

  /// تطبيق الألوان على البيانات
  List<ChartDataPoint> _applyColorsToData(List<ChartDataPoint> data, Map<String, Color> colors) {
    return data.map((dataPoint) {
      final savedColor = colors[dataPoint.label];
      if (savedColor != null) {
        return dataPoint.copyWith(color: savedColor);
      }
      return dataPoint;
    }).toList();
  }

  /// تطبيق الألوان المحفوظة للمستخدم على البيانات الجديدة
  Future<List<ChartDataPoint>> _applyUserColorsToData(String cardId, List<ChartDataPoint> data) async {
    try {
      final savedColors = await ColorPreferencesService.getCardColors(cardId);
      if (savedColors.isNotEmpty) {
        debugPrint('🎨 تطبيق ألوان محفوظة على بيانات البطاقة $cardId');
        return _applyColorsToData(data, savedColors);
      }
    } catch (e) {
      debugPrint('خطأ في تطبيق الألوان المحفوظة: $e');
    }
    return data;
  }

  /// تهيئة لوحة التحكم
  Future<void> _initializeDashboard() async {
    await loadDashboardData();
    _createDefaultChartCards();
    _createDefaultLayouts();
    await _loadPreferences(); // تحميل التفضيلات المحفوظة
    await loadSavedColors(); // تحميل الألوان المحفوظة
  }

  /// تحميل جميع بيانات لوحة التحكم
  Future<void> loadDashboardData({bool forceRefresh = false}) async {
    if (_isLoading.value && !forceRefresh) return;

    _isLoading.value = true;
    _error.value = '';

    try {
      // تحميل الإحصائيات بشكل متوازي (بدون فلاتر عامة)
      final futures = await Future.wait([
        _dataService.getTaskStatistics(),
        _dataService.getUserStatistics(),
      ]);

      _taskStatistics.value = futures[0] as TaskStatistics;
      _userStatistics.value = futures[1] as UserStatistics;

      // تحديث بيانات البطاقات
      await _updateChartCardsData();

      debugPrint('✅ تم تحميل بيانات لوحة التحكم بنجاح');
    } catch (e) {
      _error.value = 'خطأ في تحميل بيانات لوحة التحكم: $e';
      debugPrint('❌ خطأ في تحميل بيانات لوحة التحكم: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// تحديث البيانات (Pull to Refresh)
  Future<void> refreshDashboard() async {
    _isRefreshing.value = true;
    await loadDashboardData(forceRefresh: true);
    _isRefreshing.value = false;
  }

  /// تحديث بطاقة واحدة فقط
  Future<void> refreshSingleCard(String cardId) async {
    try {
      debugPrint('🔄 بدء تحديث البطاقة: $cardId');

      // العثور على البطاقة
      final cardIndex = _chartCards.indexWhere((card) => card.id == cardId);
      if (cardIndex == -1) {
        debugPrint('❌ لم يتم العثور على البطاقة: $cardId');
        return;
      }

      final card = _chartCards[cardIndex];

      // تحديث البيانات حسب نوع البطاقة
      await _refreshCardData(card);

      debugPrint('✅ تم تحديث البطاقة بنجاح: $cardId');
    } catch (e) {
      debugPrint('❌ خطأ في تحديث البطاقة $cardId: $e');
    }
  }

  /// تحديث بيانات بطاقة محددة باستخدام TaskController القوي
  Future<void> _refreshCardData(ChartCardModel card) async {
    try {
      // تطبيق فلاتر البطاقة على TaskController
      _applyCardFiltersToTaskController(card.cardFilters);

      // الحصول على البيانات المفلترة من TaskController
      final filteredTasks = _taskController.filteredTasks;

      // تحويل المهام المفلترة إلى بيانات المخطط
      List<ChartDataPoint> newData = [];

      switch (card.dataSource) {
        case 'tasks_status':
          newData = _groupTasksByStatus(filteredTasks);
          break;
        case 'tasks_priority':
          newData = _groupTasksByPriority(filteredTasks);
          break;
        case 'tasks_creator':
          newData = _groupTasksByCreator(filteredTasks);
          break;
        case 'tasks_assignee':
          newData = _groupTasksByAssignee(filteredTasks);
          break;
        case 'tasks_department':
          newData = await _groupTasksByDepartment(filteredTasks);
          break;
        case 'tasks_access':
          newData = _groupTasksByUserAccess(filteredTasks);
          break;
        case 'contributors_by_status':
          newData = _groupContributorsByStatus(filteredTasks);
          break;
        case 'user_activity':
          newData = _groupTasksByContributorCount(filteredTasks);
          break;
        case 'tasks_type':
          newData = _groupTasksByType(filteredTasks);
          break;
        default:
          debugPrint('⚠️ مصدر بيانات غير معروف للبطاقة: ${card.dataSource}');
          return;
      }

      debugPrint('🔄 تم تحديث بيانات البطاقة ${card.id}: ${newData.length} عنصر من ${filteredTasks.length} مهمة مفلترة');

      // تحديث بيانات البطاقة نفسها
      await _updateSingleCardData(card, newData);

    } catch (e) {
      debugPrint('❌ خطأ في تحديث بيانات البطاقة ${card.id}: $e');
    }
  }

  /// تطبيق فلاتر البطاقة على TaskController
  void _applyCardFiltersToTaskController(Map<String, DataFilter> cardFilters) {
    // مسح الفلاتر الحالية
    _taskController.clearFilters();

    for (final filter in cardFilters.values) {
      if (!filter.isActive || filter.value == null) continue;

      switch (filter.id) {
        case 'status':
          if (filter.value is List) {
            // للفلاتر متعددة الاختيار، نطبق أول قيمة فقط (يمكن تحسينها لاحقاً)
            final statusList = filter.value as List;
            if (statusList.isNotEmpty) {
              _taskController.setStatusFilter(statusList.first.toString());
            }
          } else {
            _taskController.setStatusFilter(filter.value.toString());
          }
          break;

        case 'priority':
          if (filter.value is List) {
            final priorityList = filter.value as List;
            if (priorityList.isNotEmpty) {
              _taskController.setPriorityFilter(priorityList.first.toString());
            }
          } else {
            _taskController.setPriorityFilter(filter.value.toString());
          }
          break;

        case 'department':
          // البحث عن معرف القسم بناءً على الاسم
          // يمكن تحسين هذا لاحقاً بحفظ معرفات الأقسام
          break;

        case 'assignee':
          if (filter.value is int) {
            _taskController.setAssigneeFilter(filter.value as int);
          }
          break;

        case 'created_date_range':
        case 'start_date_range':
        case 'due_date_range':
        case 'completed_date_range':
          // فلاتر التاريخ - يمكن إضافتها لاحقاً إلى TaskController
          break;
      }
    }

    debugPrint('🔍 تم تطبيق فلاتر البطاقة على TaskController');
  }

  /// تجميع المهام حسب الحالة
  List<ChartDataPoint> _groupTasksByStatus(List<Task> tasks) {
    final statusGroups = <String, int>{};

    for (final task in tasks) {
      statusGroups[task.status] = (statusGroups[task.status] ?? 0) + 1;
    }

    return statusGroups.entries.map((entry) =>
      ChartDataPoint(label: entry.key, value: entry.value.toDouble())
    ).toList();
  }

  /// تجميع المهام حسب الأولوية
  List<ChartDataPoint> _groupTasksByPriority(List<Task> tasks) {
    final priorityGroups = <String, int>{};

    for (final task in tasks) {
      priorityGroups[task.priority] = (priorityGroups[task.priority] ?? 0) + 1;
    }

    return priorityGroups.entries.map((entry) =>
      ChartDataPoint(label: entry.key, value: entry.value.toDouble())
    ).toList();
  }

  /// تجميع المهام حسب المنشئ
  List<ChartDataPoint> _groupTasksByCreator(List<Task> tasks) {
    final creatorGroups = <String, int>{};

    for (final task in tasks) {
      final creatorName = task.creator?.name ?? 'غير محدد';
      creatorGroups[creatorName] = (creatorGroups[creatorName] ?? 0) + 1;
    }

    return creatorGroups.entries.map((entry) =>
      ChartDataPoint(label: entry.key, value: entry.value.toDouble())
    ).toList();
  }

  /// تجميع المهام حسب المكلف
  List<ChartDataPoint> _groupTasksByAssignee(List<Task> tasks) {
    final assigneeGroups = <String, int>{};

    for (final task in tasks) {
      final assigneeName = task.assignee?.name ?? 'غير مكلف';
      assigneeGroups[assigneeName] = (assigneeGroups[assigneeName] ?? 0) + 1;
    }

    return assigneeGroups.entries.map((entry) =>
      ChartDataPoint(label: entry.key, value: entry.value.toDouble())
    ).toList();
  }

  /// تجميع المهام حسب القسم
  Future<List<ChartDataPoint>> _groupTasksByDepartment(List<Task> tasks) async {
    final departmentGroups = <String, int>{};

    for (final task in tasks) {
      final departmentName = task.department?.name ?? 'بدون قسم';
      departmentGroups[departmentName] = (departmentGroups[departmentName] ?? 0) + 1;
    }

    return departmentGroups.entries.map((entry) =>
      ChartDataPoint(label: entry.key, value: entry.value.toDouble())
    ).toList();
  }

  /// تجميع المهام حسب الوصول
  List<ChartDataPoint> _groupTasksByUserAccess(List<Task> tasks) {
    final accessGroups = <String, int>{};

    for (final task in tasks) {
      final accessCount = task.accessUserIds?.length ?? 0;
      final accessLabel = accessCount == 0 ? 'وصول محدود' : 'وصول متعدد ($accessCount)';
      accessGroups[accessLabel] = (accessGroups[accessLabel] ?? 0) + 1;
    }

    return accessGroups.entries.map((entry) =>
      ChartDataPoint(label: entry.key, value: entry.value.toDouble())
    ).toList();
  }

  /// تجميع المساهمين حسب الحالة
  List<ChartDataPoint> _groupContributorsByStatus(List<Task> tasks) {
    final contributorGroups = <String, int>{};

    for (final task in tasks) {
      final contributorCount = (task.comments.length + task.attachments.length);
      final statusLabel = '${task.status} ($contributorCount مساهمة)';
      contributorGroups[statusLabel] = (contributorGroups[statusLabel] ?? 0) + 1;
    }

    return contributorGroups.entries.map((entry) =>
      ChartDataPoint(label: entry.key, value: entry.value.toDouble())
    ).toList();
  }

  /// تجميع المهام حسب عدد المساهمين
  List<ChartDataPoint> _groupTasksByContributorCount(List<Task> tasks) {
    final contributorGroups = <String, int>{};

    for (final task in tasks) {
      final contributorCount = (task.comments.length + task.attachments.length);
      final countLabel = contributorCount == 0 ? 'بدون مساهمات' :
                        contributorCount <= 5 ? 'مساهمات قليلة' : 'مساهمات كثيرة';
      contributorGroups[countLabel] = (contributorGroups[countLabel] ?? 0) + 1;
    }

    return contributorGroups.entries.map((entry) =>
      ChartDataPoint(label: entry.key, value: entry.value.toDouble())
    ).toList();
  }

  /// تجميع المهام حسب النوع
  List<ChartDataPoint> _groupTasksByType(List<Task> tasks) {
    final typeGroups = <String, int>{};

    for (final task in tasks) {
      final typeName = task.taskType?.name ?? 'بدون نوع محدد';
      typeGroups[typeName] = (typeGroups[typeName] ?? 0) + 1;
    }

    return typeGroups.entries.map((entry) =>
      ChartDataPoint(label: entry.key, value: entry.value.toDouble())
    ).toList();
  }

  /// تحويل فلاتر البطاقة إلى خريطة للاستخدام مع API
  Map<String, dynamic> _getCardFiltersMap(Map<String, DataFilter> cardFilters) {
    final filtersMap = <String, dynamic>{};
    for (final filter in cardFilters.values) {
      if (filter.isActive && filter.value != null) {
        filtersMap[filter.id] = filter.value;
      }
    }
    return filtersMap;
  }

  /// تحديث بيانات بطاقة واحدة
  Future<void> _updateSingleCardData(ChartCardModel card, List<ChartDataPoint> newData) async {
    try {
      // العثور على البطاقة في القائمة
      final cardIndex = _chartCards.indexWhere((c) => c.id == card.id);
      if (cardIndex == -1) return;

      // تحميل الألوان المحفوظة من التفضيلات
      final savedColors = await ColorPreferencesService.getCardColors(card.id);
      debugPrint('🎨 تم تحميل ${savedColors.length} لون محفوظ للبطاقة ${card.id}');

      // تطبيق الألوان المحفوظة على البيانات الجديدة
      final dataWithColors = _applyColorsToData(newData, savedColors);
      debugPrint('🎨 تم تطبيق الألوان على ${dataWithColors.length} نقطة بيانات');

      // تحديث البطاقة بالبيانات الجديدة والألوان المحفوظة (مع الحفاظ على الفلاتر)
      final updatedCard = card.copyWith(
        data: dataWithColors,
        colorMapping: savedColors, // تحديث خريطة الألوان أيضاً
        lastUpdated: DateTime.now(),
        // الحفاظ على الفلاتر الموجودة
        cardFilters: card.cardFilters,
      );

      _chartCards[cardIndex] = updatedCard;

      // تحديث متعدد ومكثف للواجهة
      _chartCards.refresh(); // إجبار تحديث القائمة
      update(['chart_card_${card.id}']); // تحديث البطاقة المحددة
      update(); // تحديث عام

      debugPrint('🔄 تم تحديث البطاقة ${card.id} في الفهرس $cardIndex');
      debugPrint('📊 البيانات الجديدة: ${updatedCard.data.length} عنصر');

      // تحديث إضافي مؤجل لضمان ظهور التغييرات
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _chartCards.refresh();
        update();
        debugPrint('✅ تم تحديث واجهة البطاقة ${card.id} نهائياً');
      });

      debugPrint('✅ تم تحديث بيانات البطاقة: ${card.id} مع ${newData.length} عنصر و ${savedColors.length} لون');
    } catch (e) {
      debugPrint('❌ خطأ في تحديث بيانات البطاقة ${card.id}: $e');
    }
  }

  /// إنشاء البطاقات الافتراضية
  void _createDefaultChartCards() {
    final defaultCards = DashboardConfig.getDefaultChartCards();
    _chartCards.assignAll(defaultCards);
  }

  /// إنشاء التخطيطات الافتراضية
  void _createDefaultLayouts() {
    final defaultLayouts = DashboardConfig.getDefaultLayouts();
    _layouts.assignAll(defaultLayouts);
    _currentLayout.value = defaultLayouts.firstWhere((layout) => layout.isDefault);
  }

  /// تحديث بيانات البطاقات
  Future<void> _updateChartCardsData() async {
    // كل بطاقة تستخدم فلاترها الخاصة فقط

    for (int i = 0; i < _chartCards.length; i++) {
      final card = _chartCards[i];

      // استخدام فلاتر البطاقة الخاصة بها فقط
      final cardFiltersMap = _getCardFiltersMap(card.cardFilters);
      List<ChartDataPoint> newData = [];

      try {
        switch (card.dataSource) {
          case 'tasks_status':
            newData = await _dataService.getTasksByStatus(filters: cardFiltersMap);
            break;
          case 'tasks_priority':
            newData = await _dataService.getTasksByPriority(filters: cardFiltersMap);
            break;
          case 'tasks_creator':
            newData = await _dataService.getTasksByCreator(filters: cardFiltersMap);
            break;
          case 'tasks_assignee':
            newData = await _dataService.getTasksByAssignee(filters: cardFiltersMap);
            break;
          case 'tasks_department':
            newData = await _dataService.getTasksByDepartment(filters: cardFiltersMap);
            break;
          case 'tasks_access':
            newData = await _dataService.getTasksByUserAccess(filters: cardFiltersMap);
            break;
          case 'contributors_by_status':
            newData = await _dataService.getContributorsByStatus(filters: cardFiltersMap);
            break;
          case 'user_activity':
            newData = await _dataService.getTasksByContributorCount(filters: cardFiltersMap);
            break;
          case 'contributor_matrix':
            newData = await _dataService.getContributorMatrix(filters: cardFiltersMap);
            break;
          case 'assignee_matrix':
            newData = await _dataService.getAssigneeMatrix(filters: cardFiltersMap);
            break;
          case 'task_trends':
            newData = await _dataService.getTaskTrends(filters: cardFiltersMap);
            break;
          case 'department_performance':
            newData = await _dataService.getDepartmentPerformance(filters: cardFiltersMap);
            break;
          case 'tasks_type':
            newData = await _dataService.getTasksByType(filters: cardFiltersMap);
            break;
        }

        // تطبيق الألوان المحفوظة على البيانات الجديدة
        final dataWithColors = await _applyUserColorsToData(card.id, newData);

        _chartCards[i] = card.copyWith(
          data: dataWithColors,
          lastUpdated: DateTime.now(),
        );

        debugPrint('✅ تم تحديث بيانات البطاقة ${card.id}: ${dataWithColors.length} عنصر');
      } catch (e) {
        debugPrint('خطأ في تحديث بيانات البطاقة ${card.id}: $e');
      }
    }
  }

  /// تغيير نوع المخطط لبطاقة معينة
  void changeChartType(String cardId, ChartType newType) {
    final cardIndex = _chartCards.indexWhere((card) => card.id == cardId);
    if (cardIndex != -1) {
      final card = _chartCards[cardIndex];
      if (card.supportedChartTypes.contains(newType)) {
        _chartCards[cardIndex] = card.copyWith(chartType: newType);
        _chartCards.refresh(); // إجبار تحديث الواجهة

        // حفظ التغيير في التفضيلات
        _savePreferences();

        debugPrint('تم تغيير نوع المخطط للبطاقة $cardId إلى $newType');
      }
    }
  }

  /// تغيير حجم البطاقة
  void resizeCard(String cardId, Size newSize) {
    final cardIndex = _chartCards.indexWhere((card) => card.id == cardId);
    if (cardIndex != -1) {
      final card = _chartCards[cardIndex];
      _chartCards[cardIndex] = card.copyWith(size: newSize);
    }
  }



  /// تحديث إعدادات لوحة التحكم
  void updateSettings(DashboardSettings newSettings) {
    _settings.value = newSettings;
    // حفظ الإعدادات محلياً إذا لزم الأمر
  }

  /// تغيير التخطيط الحالي
  void changeLayout(String layoutId) {
    final layout = _layouts.firstWhereOrNull((l) => l.id == layoutId);
    if (layout != null) {
      _currentLayout.value = layout;
    }
  }



  /// تصدير بيانات لوحة التحكم
  Map<String, dynamic> exportDashboardData() {
    return {
      'taskStatistics': {
        'totalTasks': _taskStatistics.value.totalTasks,
        'completedTasks': _taskStatistics.value.completedTasks,
        'pendingTasks': _taskStatistics.value.pendingTasks,
        'inProgressTasks': _taskStatistics.value.inProgressTasks,
        'overdueTasks': _taskStatistics.value.overdueTasks,
      },
      'userStatistics': {
        'totalUsers': _userStatistics.value.totalUsers,
        'activeUsers': _userStatistics.value.activeUsers,
        'inactiveUsers': _userStatistics.value.inactiveUsers,
      },
      'chartCards': _chartCards.map((card) => {
        'id': card.id,
        'title': card.title,
        'chartType': card.chartType.name,
        'dataCount': card.data.length,
        'lastUpdated': card.lastUpdated.toIso8601String(),
      }).toList(),
      'cardFilters': _chartCards.map((card) => {
        'cardId': card.id,
        'filtersCount': card.cardFilters.length,
      }).toList(),
      'exportedAt': DateTime.now().toIso8601String(),
    };
  }

  /// تحديث بطاقة كاملة
  void updateCard(ChartCardModel updatedCard) {
    final cardIndex = _chartCards.indexWhere((card) => card.id == updatedCard.id);
    if (cardIndex != -1) {
      _chartCards[cardIndex] = updatedCard;
      _chartCards.refresh();
    }
  }

  /// إعادة ترتيب بطاقات المخططات
  void reorderChartCards(List<ChartCardModel> reorderedCards) {
    _chartCards.clear();
    _chartCards.addAll(reorderedCards);
    _chartCards.refresh();

    // حفظ الترتيب الجديد
    _savePreferences();

    // إضافة تسجيل للنشاط
    debugPrint('تم إعادة ترتيب ${reorderedCards.length} بطاقة مخطط');
  }

  /// حفظ التفضيلات
  Future<void> _savePreferences() async {
    try {
      // حفظ إعدادات لوحة التحكم
      await _preferencesService.saveDashboardSettings(_settings.value);

      // حفظ ترتيب البطاقات
      final cardOrder = _chartCards.map((card) => card.id).toList();
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList('dashboard_card_order', cardOrder);

      // حفظ أحجام البطاقات
      final cardSizes = <String, Size>{};
      for (final card in _chartCards) {
        cardSizes[card.id] = card.size;
      }
      await _preferencesService.saveCardSizes(cardSizes);

      // حفظ خرائط الألوان
      final colorMappings = <String, Map<String, Color>>{};
      for (final card in _chartCards) {
        if (card.colorMapping.isNotEmpty) {
          colorMappings[card.id] = card.colorMapping;
        }
      }
      await _preferencesService.saveColorMappings(colorMappings);

      // حفظ فلاتر البطاقات (لا توجد فلاتر عامة)
      // فلاتر البطاقات تُحفظ مع البطاقات نفسها

      debugPrint('تم حفظ التفضيلات بنجاح');
    } catch (e) {
      debugPrint('خطأ في حفظ التفضيلات: $e');
    }
  }

  /// تحميل التفضيلات
  Future<void> _loadPreferences() async {
    try {
      // تحميل إعدادات لوحة التحكم
      final settings = await _preferencesService.loadDashboardSettings();
      if (settings != null) {
        _settings.value = settings;
      }

      // تحميل أحجام البطاقات
      final cardSizes = await _preferencesService.loadCardSizes();

      // تحميل خرائط الألوان
      final colorMappings = await _preferencesService.loadColorMappings();

      // تطبيق التفضيلات على البطاقات
      for (int i = 0; i < _chartCards.length; i++) {
        final card = _chartCards[i];
        final savedSize = cardSizes[card.id];
        final savedColors = colorMappings[card.id];

        if (savedSize != null || savedColors != null) {
          _chartCards[i] = card.copyWith(
            size: savedSize ?? card.size,
            colorMapping: savedColors ?? card.colorMapping,
          );
        }
      }

      // تحميل فلاتر البطاقات (لا توجد فلاتر عامة)
      // فلاتر البطاقات تُحمل مع البطاقات نفسها

      _chartCards.refresh();
      debugPrint('تم تحميل التفضيلات بنجاح');
    } catch (e) {
      debugPrint('خطأ في تحميل التفضيلات: $e');
    }
  }

  /// إعادة تعيين جميع التفضيلات
  Future<void> resetPreferences() async {
    try {
      await _preferencesService.clearAllPreferences();

      // إعادة تحميل البطاقات الافتراضية
      _chartCards.clear();
      _chartCards.addAll(DashboardConfig.getDefaultChartCards());

      // إعادة تعيين الإعدادات
      _settings.value = const DashboardSettings();

      // إعادة تعيين فلاتر البطاقات
      for (int i = 0; i < _chartCards.length; i++) {
        _chartCards[i] = _chartCards[i].copyWith(cardFilters: {});
      }

      _chartCards.refresh();
      debugPrint('تم إعادة تعيين جميع التفضيلات');
    } catch (e) {
      debugPrint('خطأ في إعادة تعيين التفضيلات: $e');
    }
  }

  /// تطبيق فلاتر على بطاقة محددة (لا توجد فلاتر عامة)
  void applyFiltersToCard(String cardId, Map<String, DataFilter> filters) {
    try {
      final cardIndex = _chartCards.indexWhere((card) => card.id == cardId);
      if (cardIndex != -1) {
        _chartCards[cardIndex] = _chartCards[cardIndex].copyWith(cardFilters: filters);

        // تحديث البطاقة المحددة فقط
        refreshSingleCard(cardId);

        debugPrint('تم تطبيق ${filters.length} فلتر على البطاقة $cardId');
      }
    } catch (e) {
      debugPrint('خطأ في تطبيق فلاتر البطاقة: $e');
    }
  }

  /// تحديث ألوان بطاقة معينة
  Future<void> updateCardColors(String cardId, Map<String, Color> newColors) async {
    try {
      debugPrint('🎨 بدء تحديث ألوان البطاقة $cardId');
      debugPrint('🎨 الألوان الجديدة: ${newColors.length} لون');

      final cardIndex = _chartCards.indexWhere((card) => card.id == cardId);
      if (cardIndex != -1) {
        final oldCard = _chartCards[cardIndex];

        // تحديث البيانات مع الألوان الجديدة
        final updatedData = _applyColorsToData(oldCard.data, newColors);
        debugPrint('🎨 تم تحديث ${updatedData.length} نقطة بيانات');

        // تحديث البطاقة في الذاكرة مع البيانات والألوان
        _chartCards[cardIndex] = oldCard.copyWith(
          colorMapping: newColors,
          data: updatedData,
        );

        // تحديث فوري وقوي للواجهة
        _chartCards.refresh();
        update(); // تحديث عام أولاً
        update(['chart_card_$cardId']); // تحديث محدد للبطاقة

        // تحديث إضافي للتأكد من التطبيق
        WidgetsBinding.instance.addPostFrameCallback((_) {
          update();
        });

        // حفظ الألوان في التفضيلات (في الخلفية)
        ColorPreferencesService.saveCardColors(cardId, newColors).catchError((e) {
          debugPrint('❌ خطأ في حفظ الألوان: $e');
        });

        debugPrint('✅ تم تحديث ألوان البطاقة $cardId بنجاح');
        debugPrint('✅ البطاقة المحدثة: ${_chartCards[cardIndex].colorMapping.length} لون');
      } else {
        debugPrint('❌ لم يتم العثور على البطاقة $cardId');
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحديث ألوان البطاقة $cardId: $e');
    }
  }

  /// تحديث فلاتر بطاقة معينة
  Future<void> updateCardFilters(String cardId, Map<String, DataFilter> newFilters) async {
    try {
      debugPrint('🔍 بدء تحديث فلاتر البطاقة $cardId');
      debugPrint('🔍 الفلاتر الجديدة: ${newFilters.length} فلتر');

      final cardIndex = _chartCards.indexWhere((card) => card.id == cardId);
      if (cardIndex != -1) {
        final oldCard = _chartCards[cardIndex];

        // تحديث البطاقة مع الفلاتر الجديدة
        final updatedCard = oldCard.copyWith(cardFilters: newFilters);
        _chartCards[cardIndex] = updatedCard;

        // تحديث فوري للواجهة
        _chartCards.refresh();
        update(['chart_card_$cardId']);
        update(); // تحديث عام إضافي

        debugPrint('🔄 بدء تحديث بيانات البطاقة $cardId مع ${newFilters.length} فلتر');

        // تحديث البيانات مع الفلاتر الجديدة
        await _refreshCardDataWithFilters(updatedCard, newFilters);

        debugPrint('✅ تم تحديث فلاتر وبيانات البطاقة $cardId بنجاح');
      } else {
        debugPrint('❌ لم يتم العثور على البطاقة $cardId');
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحديث فلاتر البطاقة $cardId: $e');
    }
  }

  /// تحديث بيانات البطاقة مع الفلاتر الجديدة
  Future<void> _refreshCardDataWithFilters(ChartCardModel card, Map<String, DataFilter> filters) async {
    // تحويل فلاتر البطاقة إلى خريطة للاستخدام مع API
    final filtersMap = <String, dynamic>{};
    for (final filter in filters.values) {
      if (filter.isActive && filter.value != null) {
        filtersMap[filter.id] = filter.value;
      }
    }

    debugPrint('🔍 تطبيق فلاتر البطاقة: $filtersMap');

    // تحديث البيانات مع الفلاتر المحددة
    await _refreshCardDataWithSpecificFilters(card, filtersMap);
  }

  /// تحديث بيانات البطاقة مع فلاتر محددة
  Future<void> _refreshCardDataWithSpecificFilters(ChartCardModel card, Map<String, dynamic> filters) async {
    try {
      List<ChartDataPoint> newData = [];

      switch (card.dataSource) {
        case 'tasks_status':
          newData = await _dataService.getTasksByStatus(filters: filters);
          break;
        case 'tasks_priority':
          newData = await _dataService.getTasksByPriority(filters: filters);
          break;
        case 'tasks_creator':
          newData = await _dataService.getTasksByCreator(filters: filters);
          break;
        case 'tasks_assignee':
          newData = await _dataService.getTasksByAssignee(filters: filters);
          break;
        case 'tasks_department':
          newData = await _dataService.getTasksByDepartment(filters: filters);
          break;
        case 'tasks_access':
          newData = await _dataService.getTasksByUserAccess(filters: filters);
          break;
        case 'contributors_by_status':
          newData = await _dataService.getContributorsByStatus(filters: filters);
          break;
        case 'user_activity':
          newData = await _dataService.getTasksByContributorCount(filters: filters);
          break;
        case 'contributor_matrix':
          newData = await _dataService.getContributorMatrix(filters: filters);
          break;
        case 'assignee_matrix':
          newData = await _dataService.getAssigneeMatrix(filters: filters);
          break;
        case 'task_trends':
          newData = await _dataService.getTaskTrends(filters: filters);
          break;
        case 'department_performance':
          newData = await _dataService.getDepartmentPerformance(filters: filters);
          break;
        case 'tasks_type':
          newData = await _dataService.getTasksByType(filters: filters);
          break;
        default:
          debugPrint('⚠️ مصدر بيانات غير معروف للبطاقة: ${card.dataSource}');
          return;
      }

      debugPrint('🔄 تم تحديث بيانات البطاقة ${card.id} مع الفلاتر: ${newData.length} عنصر');

      // تحديث بيانات البطاقة نفسها
      await _updateSingleCardData(card, newData);

      // تحديث فوري ومتعدد للواجهة لضمان ظهور التغييرات
      _chartCards.refresh();
      update(['chart_card_${card.id}']);
      update();

      // إجبار إعادة بناء البطاقة بتحديث timestamp
      final cardIndex = _chartCards.indexWhere((c) => c.id == card.id);
      if (cardIndex != -1) {
        _chartCards[cardIndex] = _chartCards[cardIndex].copyWith(
          lastUpdated: DateTime.now(),
        );
        _chartCards.refresh();
      }

      // تحديث إضافي مؤجل
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _chartCards.refresh();
        update();
        debugPrint('✅ تم تحديث واجهة البطاقة ${card.id} بنجاح');
      });

    } catch (e) {
      debugPrint('❌ خطأ في تحديث بيانات البطاقة ${card.id} مع الفلاتر: $e');
    }
  }

  /// الحصول على الفلاتر المتاحة للبطاقة من البيانات الحقيقية
  Future<List<DataFilter>> getAvailableFiltersForCard(String cardId) async {
    try {
      final card = _chartCards.firstWhere((c) => c.id == cardId);

      switch (card.dataSource) {
        case 'tasks_status':
        case 'tasks_priority':
        case 'tasks_creator':
        case 'tasks_assignee':
          return await _getTaskFilters();
        case 'tasks_department':
          return await _getDepartmentFilters();
        case 'tasks_access':
        case 'contributors_by_status':
          return await _getUserFilters();
        default:
          return [];
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل فلاتر البطاقة $cardId: $e');
      return [];
    }
  }

  /// الحصول على فلاتر المهام من البيانات الحقيقية
  Future<List<DataFilter>> _getTaskFilters() async {
    final filters = <DataFilter>[];

    try {
      // تحميل البيانات الحقيقية من المهام الموجودة
      final tasksByDepartment = await _dataService.getTasksByDepartment();
      final departmentNames = tasksByDepartment.map((d) => d.label).toSet().toList();

      if (departmentNames.isNotEmpty) {
        filters.add(DataFilter(
          id: 'department',
          name: 'القسم',
          type: FilterType.dropdown,
          options: departmentNames,
        ));
      }

      // تحميل حالات المهام من البيانات الحقيقية
      final tasksByStatus = await _dataService.getTasksByStatus();
      final statusNames = tasksByStatus.map((s) => s.label).toSet().toList();

      if (statusNames.isNotEmpty) {
        filters.add(DataFilter(
          id: 'status',
          name: 'الحالة',
          type: FilterType.multiSelect,
          options: statusNames,
        ));
      }

      // تحميل أولويات المهام من البيانات الحقيقية
      final tasksByPriority = await _dataService.getTasksByPriority();
      final priorityNames = tasksByPriority.map((p) => p.label).toSet().toList();

      if (priorityNames.isNotEmpty) {
        filters.add(DataFilter(
          id: 'priority',
          name: 'الأولوية',
          type: FilterType.multiSelect,
          options: priorityNames,
        ));
      }

      // إضافة فلاتر التاريخ المختلفة
      filters.add(DataFilter(
        id: 'created_date_range',
        name: 'نطاق تاريخ الإنشاء',
        type: FilterType.dateRange,
      ));

      filters.add(DataFilter(
        id: 'start_date_range',
        name: 'نطاق تاريخ البداية',
        type: FilterType.dateRange,
      ));

      filters.add(DataFilter(
        id: 'due_date_range',
        name: 'نطاق تاريخ الاستحقاق',
        type: FilterType.dateRange,
      ));

      filters.add(DataFilter(
        id: 'completed_date_range',
        name: 'نطاق تاريخ الإكمال',
        type: FilterType.dateRange,
      ));

    } catch (e) {
      debugPrint('❌ خطأ في تحميل فلاتر المهام: $e');
    }

    return filters;
  }

  /// الحصول على فلاتر الأقسام من البيانات الحقيقية
  Future<List<DataFilter>> _getDepartmentFilters() async {
    final filters = <DataFilter>[];

    try {
      // تحميل حالات المهام من البيانات الحقيقية
      final tasksByStatus = await _dataService.getTasksByStatus();
      final statusNames = tasksByStatus.map((s) => s.label).toSet().toList();

      if (statusNames.isNotEmpty) {
        filters.add(DataFilter(
          id: 'status',
          name: 'الحالة',
          type: FilterType.multiSelect,
          options: statusNames,
        ));
      }

      // إضافة فلاتر التاريخ المختلفة
      filters.add(DataFilter(
        id: 'created_date_range',
        name: 'نطاق تاريخ الإنشاء',
        type: FilterType.dateRange,
      ));

      filters.add(DataFilter(
        id: 'due_date_range',
        name: 'نطاق تاريخ الاستحقاق',
        type: FilterType.dateRange,
      ));

    } catch (e) {
      debugPrint('❌ خطأ في تحميل فلاتر الأقسام: $e');
    }

    return filters;
  }

  /// الحصول على فلاتر المستخدمين من البيانات الحقيقية
  Future<List<DataFilter>> _getUserFilters() async {
    final filters = <DataFilter>[];

    try {
      // تحميل الأقسام من بيانات المهام
      final tasksByDepartment = await _dataService.getTasksByDepartment();
      final departmentNames = tasksByDepartment.map((d) => d.label).toSet().toList();

      if (departmentNames.isNotEmpty) {
        filters.add(DataFilter(
          id: 'department',
          name: 'القسم',
          type: FilterType.dropdown,
          options: departmentNames,
        ));
      }

      // إضافة فلاتر التاريخ المختلفة
      filters.add(DataFilter(
        id: 'created_date_range',
        name: 'نطاق تاريخ الإنشاء',
        type: FilterType.dateRange,
      ));

    } catch (e) {
      debugPrint('❌ خطأ في تحميل فلاتر المستخدمين: $e');
    }

    return filters;
  }

  @override
  void onClose() {
    // تنظيف الموارد
    super.onClose();
  }
}
