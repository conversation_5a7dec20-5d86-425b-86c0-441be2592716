# أنواع فلاتر التاريخ في النظام

## أعمدة التاريخ المتاحة في نموذج المهمة 📅

### 1. تاريخ الإنشاء (createdAt)
- **العمود**: `createdAt`
- **الوصف**: تاريخ إنشاء المهمة
- **النوع**: `long` (Unix timestamp بالثواني)
- **مطلوب**: نعم ✅
- **الاستخدام**: لمعرفة متى تم إنشاء المهمة

### 2. تاريخ البداية (startDate)
- **العمود**: `startDate`
- **الوصف**: تاريخ بداية العمل على المهمة
- **النوع**: `long?` (Unix timestamp بالثواني، اختياري)
- **مطلوب**: لا ❌
- **الاستخدام**: لمعرفة متى بدأ العمل الفعلي على المهمة

### 3. تاريخ الاستحقاق (dueDate)
- **العمود**: `dueDate`
- **الوصف**: تاريخ استحقاق المهمة (الموعد النهائي)
- **النوع**: `long?` (Unix timestamp بالثواني، اختياري)
- **مطلوب**: لا ❌
- **الاستخدام**: لمعرفة الموعد النهائي لإنجاز المهمة

### 4. تاريخ الإكمال (completedAt)
- **العمود**: `completedAt`
- **الوصف**: تاريخ إكمال المهمة
- **النوع**: `long?` (Unix timestamp بالثواني، اختياري)
- **مطلوب**: لا ❌
- **الاستخدام**: لمعرفة متى تم إكمال المهمة فعلياً

## فلاتر التاريخ المتاحة في النظام 🎛️

### 1. فلتر تاريخ الإنشاء
```dart
DataFilter(
  id: 'created_date_range',
  name: 'نطاق تاريخ الإنشاء',
  type: FilterType.dateRange,
)
```
- **معاملات API**: `created_at_start`, `created_at_end`
- **الاستخدام**: لفلترة المهام حسب تاريخ إنشائها
- **مثال**: عرض المهام المنشأة في الشهر الماضي

### 2. فلتر تاريخ البداية
```dart
DataFilter(
  id: 'start_date_range',
  name: 'نطاق تاريخ البداية',
  type: FilterType.dateRange,
)
```
- **معاملات API**: `start_date_start`, `start_date_end`
- **الاستخدام**: لفلترة المهام حسب تاريخ بدايتها
- **مثال**: عرض المهام التي بدأت هذا الأسبوع

### 3. فلتر تاريخ الاستحقاق
```dart
DataFilter(
  id: 'due_date_range',
  name: 'نطاق تاريخ الاستحقاق',
  type: FilterType.dateRange,
)
```
- **معاملات API**: `due_date_start`, `due_date_end`
- **الاستخدام**: لفلترة المهام حسب تاريخ استحقاقها
- **مثال**: عرض المهام المستحقة هذا الشهر

### 4. فلتر تاريخ الإكمال
```dart
DataFilter(
  id: 'completed_date_range',
  name: 'نطاق تاريخ الإكمال',
  type: FilterType.dateRange,
)
```
- **معاملات API**: `completed_at_start`, `completed_at_end`
- **الاستخدام**: لفلترة المهام حسب تاريخ إكمالها
- **مثال**: عرض المهام المكتملة في الربع الأخير

## كيفية تحويل التواريخ 🔄

### من DateTimeRange إلى Unix Timestamp
```dart
// في _buildQueryParameters()
if (key == 'created_date_range') {
  queryParams['created_at_start'] = (value.start.millisecondsSinceEpoch ~/ 1000).toString();
  queryParams['created_at_end'] = (value.end.millisecondsSinceEpoch ~/ 1000).toString();
}
```

### من Unix Timestamp إلى DateTime
```dart
// في نموذج المهمة
DateTime get createdAtDateTime =>
    DateTime.fromMillisecondsSinceEpoch(createdAt * 1000);

DateTime? get dueDateDateTime => dueDate != null
    ? DateTime.fromMillisecondsSinceEpoch(dueDate! * 1000)
    : null;
```

## أمثلة على الاستخدام 📋

### 1. فلترة المهام المنشأة في يناير 2025
```dart
DataFilter(
  id: 'created_date_range',
  name: 'نطاق تاريخ الإنشاء',
  type: FilterType.dateRange,
  value: DateTimeRange(
    start: DateTime(2025, 1, 1),
    end: DateTime(2025, 1, 31),
  ),
  isActive: true,
)
```
**معاملات API**:
- `created_at_start=1735689600` (2025-01-01 00:00:00)
- `created_at_end=1738367999` (2025-01-31 23:59:59)

### 2. فلترة المهام المستحقة هذا الأسبوع
```dart
DataFilter(
  id: 'due_date_range',
  name: 'نطاق تاريخ الاستحقاق',
  type: FilterType.dateRange,
  value: DateTimeRange(
    start: DateTime.now().subtract(Duration(days: DateTime.now().weekday - 1)),
    end: DateTime.now().add(Duration(days: 7 - DateTime.now().weekday)),
  ),
  isActive: true,
)
```

### 3. فلترة المهام المكتملة في الشهر الماضي
```dart
DataFilter(
  id: 'completed_date_range',
  name: 'نطاق تاريخ الإكمال',
  type: FilterType.dateRange,
  value: DateTimeRange(
    start: DateTime(DateTime.now().year, DateTime.now().month - 1, 1),
    end: DateTime(DateTime.now().year, DateTime.now().month, 0),
  ),
  isActive: true,
)
```

## الفلاتر المتاحة حسب نوع البطاقة 🎯

### بطاقات المهام (tasks_status, tasks_priority, tasks_creator, tasks_assignee)
- ✅ فلتر تاريخ الإنشاء
- ✅ فلتر تاريخ البداية
- ✅ فلتر تاريخ الاستحقاق
- ✅ فلتر تاريخ الإكمال

### بطاقات الأقسام (tasks_department, department_performance)
- ✅ فلتر تاريخ الإنشاء
- ✅ فلتر تاريخ الاستحقاق

### بطاقات المستخدمين (tasks_access, contributors_by_status, user_activity)
- ✅ فلتر تاريخ الإنشاء

## رسائل التشخيص 🔍

عند تطبيق فلتر تاريخ، ستظهر رسائل مثل:
```
🔍 معاملات الاستعلام: {created_at_start: 1735689600, created_at_end: 1738367999}
🔍 استدعاء API مع فلاتر: created_at_start=1735689600&created_at_end=1738367999
📊 تم جلب 15 مهمة مع الفلاتر
```

## ملاحظات مهمة ⚠️

### 1. التوافق مع قاعدة البيانات
- تأكد من أن API يدعم معاملات التاريخ المحددة
- Unix timestamps بالثواني (ليس بالميلي ثانية)

### 2. التعامل مع القيم الفارغة
- `startDate`, `dueDate`, `completedAt` قد تكون `null`
- فلترة هذه الحقول تتطلب التحقق من وجود القيمة

### 3. المناطق الزمنية
- التواريخ تُحفظ بـ UTC
- تحويل المنطقة الزمنية يتم في العرض فقط

## الخلاصة 📝

الآن النظام يدعم 4 أنواع من فلاتر التاريخ:
1. **تاريخ الإنشاء** - متى تم إنشاء المهمة
2. **تاريخ البداية** - متى بدأ العمل على المهمة
3. **تاريخ الاستحقاق** - الموعد النهائي للمهمة
4. **تاريخ الإكمال** - متى تم إكمال المهمة

كل فلتر يرسل معاملات محددة إلى API لضمان الفلترة الصحيحة! 🎯
