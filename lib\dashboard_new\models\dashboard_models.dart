import 'package:flutter/material.dart';

/// نموذج بيانات لوحة التحكم
class DashboardData {
  final String title;
  final dynamic value;
  final String? subtitle;
  final IconData? icon;
  final Color? color;
  final String? trend;
  final double? percentage;

  const DashboardData({
    required this.title,
    required this.value,
    this.subtitle,
    this.icon,
    this.color,
    this.trend,
    this.percentage,
  });
}

/// نموذج بطاقة المخطط البياني
class ChartCardModel {
  final String id;
  final String title;
  final String description;
  final ChartType chartType;
  final List<ChartDataPoint> data;
  final List<ChartType> supportedChartTypes;
  final Map<String, dynamic> filters;
  final Map<String, DataFilter> cardFilters; // فلاتر خاصة بالبطاقة
  final Size size;
  final bool isResizable;
  final Map<String, Color> colorMapping;
  final String? dataSource;
  final DateTime lastUpdated;

  ChartCardModel({
    required this.id,
    required this.title,
    required this.description,
    required this.chartType,
    required this.data,
    required this.supportedChartTypes,
    this.filters = const {},
    this.cardFilters = const {},
    this.size = const Size(400, 300),
    this.isResizable = true,
    this.colorMapping = const {},
    this.dataSource,
    DateTime? lastUpdated,
  }) : lastUpdated = lastUpdated ?? DateTime.now();

  ChartCardModel copyWith({
    String? id,
    String? title,
    String? description,
    ChartType? chartType,
    List<ChartDataPoint>? data,
    List<ChartType>? supportedChartTypes,
    Map<String, dynamic>? filters,
    Map<String, DataFilter>? cardFilters,
    Size? size,
    bool? isResizable,
    Map<String, Color>? colorMapping,
    String? dataSource,
    DateTime? lastUpdated,
  }) {
    return ChartCardModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      chartType: chartType ?? this.chartType,
      data: data ?? this.data,
      supportedChartTypes: supportedChartTypes ?? this.supportedChartTypes,
      filters: filters ?? this.filters,
      cardFilters: cardFilters ?? this.cardFilters,
      size: size ?? this.size,
      isResizable: isResizable ?? this.isResizable,
      colorMapping: colorMapping ?? this.colorMapping,
      dataSource: dataSource ?? this.dataSource,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'chartType': chartType.toString(),
      'data': data.map((point) => point.toJson()).toList(),
      'supportedChartTypes': supportedChartTypes.map((type) => type.toString()).toList(),
      'filters': filters,
      'size': {'width': size.width, 'height': size.height},
      'isResizable': isResizable,
      'colorMapping': colorMapping.map((key, color) => MapEntry(key, color.value)),
      'dataSource': dataSource,
      'lastUpdated': lastUpdated.millisecondsSinceEpoch,
    };
  }

  /// إنشاء من JSON
  factory ChartCardModel.fromJson(Map<String, dynamic> json) {
    return ChartCardModel(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      chartType: ChartType.values.firstWhere(
        (e) => e.toString() == json['chartType'],
        orElse: () => ChartType.pie,
      ),
      data: (json['data'] as List<dynamic>?)
          ?.map((pointJson) => ChartDataPoint.fromJson(pointJson))
          .toList() ?? [],
      supportedChartTypes: (json['supportedChartTypes'] as List<dynamic>?)
          ?.map((typeStr) => ChartType.values.firstWhere(
                (e) => e.toString() == typeStr,
                orElse: () => ChartType.pie,
              ))
          .toList() ?? [ChartType.pie],
      filters: Map<String, dynamic>.from(json['filters'] ?? {}),
      size: json['size'] != null
          ? Size(json['size']['width']?.toDouble() ?? 400, json['size']['height']?.toDouble() ?? 300)
          : const Size(400, 300),
      isResizable: json['isResizable'] ?? true,
      colorMapping: (json['colorMapping'] as Map<String, dynamic>?)?.map(
            (key, value) => MapEntry(key, Color(value as int)),
          ) ?? {},
      dataSource: json['dataSource'],
      lastUpdated: json['lastUpdated'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['lastUpdated'])
          : DateTime.now(),
    );
  }
}

/// أنواع المخططات البيانية المدعومة
enum ChartType {
  pie,           // دائري
  column,        // أعمدة
  bar,           // شريطي
  line,          // خطي
  area,          // مساحي
  doughnut,      // دونات
  spline,        // منحني
  stackedColumn, // أعمدة مكدسة
  stackedBar,    // شريطي مكدس
  scatter,       // نقطي
  bubble,        // فقاعي
  funnel,        // قمعي
  pyramid,       // هرمي

  // أنواع إضافية متاحة في Syncfusion
  stepLine,      // خطي متدرج
  stackedArea,   // مساحي مكدس
  stackedArea100,// مساحي مكدس 100%
  stackedColumn100, // أعمدة مكدسة 100%
  stackedBar100, // شريطي مكدس 100%
  splineArea,    // مساحي منحني
  stepArea,      // مساحي متدرج
  rangeColumn,   // أعمدة نطاق
  rangeArea,     // مساحي نطاق
  waterfall,     // شلال
  fastLine,      // خطي سريع
  radialBar,     // شريطي دائري
}

/// نقطة بيانات المخطط
class ChartDataPoint {
  final String label;
  final double value;
  final String? category;
  final Color? color;
  final Map<String, dynamic>? metadata;

  const ChartDataPoint({
    required this.label,
    required this.value,
    this.category,
    this.color,
    this.metadata,
  });

  Map<String, dynamic> toJson() {
    return {
      'label': label,
      'value': value,
      'category': category,
      'color': color?.value.toRadixString(16),
      'metadata': metadata,
    };
  }

  factory ChartDataPoint.fromJson(Map<String, dynamic> json) {
    return ChartDataPoint(
      label: json['label'] as String,
      value: (json['value'] as num).toDouble(),
      category: json['category'] as String?,
      color: json['color'] != null ? Color(int.parse(json['color'] as String, radix: 16)) : null,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// إنشاء نسخة جديدة مع تعديل بعض الخصائص
  ChartDataPoint copyWith({
    String? label,
    double? value,
    String? category,
    Color? color,
    Map<String, dynamic>? metadata,
  }) {
    return ChartDataPoint(
      label: label ?? this.label,
      value: value ?? this.value,
      category: category ?? this.category,
      color: color ?? this.color,
      metadata: metadata ?? this.metadata,
    );
  }
}

/// نموذج مرشح البيانات
class DataFilter {
  final String id;
  final String name;
  final FilterType type;
  final dynamic value;
  final List<dynamic>? options;
  final bool isActive;

  const DataFilter({
    required this.id,
    required this.name,
    required this.type,
    this.value,
    this.options,
    this.isActive = false,
  });

  DataFilter copyWith({
    String? id,
    String? name,
    FilterType? type,
    dynamic value,
    List<dynamic>? options,
    bool? isActive,
  }) {
    return DataFilter(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      value: value ?? this.value,
      options: options ?? this.options,
      isActive: isActive ?? this.isActive,
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type.toString(),
      'value': value,
      'options': options,
      'isActive': isActive,
    };
  }

  /// إنشاء من JSON
  factory DataFilter.fromJson(Map<String, dynamic> json) {
    return DataFilter(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      type: FilterType.values.firstWhere(
        (e) => e.toString() == json['type'],
        orElse: () => FilterType.dropdown,
      ),
      value: json['value'],
      options: json['options'] as List<dynamic>?,
      isActive: json['isActive'] ?? false,
    );
  }
}

/// أنواع المرشحات
enum FilterType {
  dropdown,    // قائمة منسدلة
  dateRange,   // نطاق تاريخ
  multiSelect, // متعدد الاختيار
  slider,      // شريط تمرير
  toggle,      // تبديل
  search,      // بحث
}

/// نموذج إعدادات لوحة التحكم
class DashboardSettings {
  final bool autoRefresh;
  final Duration refreshInterval;
  final bool showGridLines;
  final bool enableAnimations;
  final String theme;
  final Map<String, dynamic> customSettings;

  const DashboardSettings({
    this.autoRefresh = false,
    this.refreshInterval = const Duration(minutes: 5),
    this.showGridLines = true,
    this.enableAnimations = true,
    this.theme = 'default',
    this.customSettings = const {},
  });

  DashboardSettings copyWith({
    bool? autoRefresh,
    Duration? refreshInterval,
    bool? showGridLines,
    bool? enableAnimations,
    String? theme,
    Map<String, dynamic>? customSettings,
  }) {
    return DashboardSettings(
      autoRefresh: autoRefresh ?? this.autoRefresh,
      refreshInterval: refreshInterval ?? this.refreshInterval,
      showGridLines: showGridLines ?? this.showGridLines,
      enableAnimations: enableAnimations ?? this.enableAnimations,
      theme: theme ?? this.theme,
      customSettings: customSettings ?? this.customSettings,
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'autoRefresh': autoRefresh,
      'refreshInterval': refreshInterval.inMilliseconds,
      'showGridLines': showGridLines,
      'enableAnimations': enableAnimations,
      'theme': theme,
      'customSettings': customSettings,
    };
  }

  /// إنشاء من JSON
  factory DashboardSettings.fromJson(Map<String, dynamic> json) {
    return DashboardSettings(
      autoRefresh: json['autoRefresh'] ?? false,
      refreshInterval: Duration(milliseconds: json['refreshInterval'] ?? 300000),
      showGridLines: json['showGridLines'] ?? true,
      enableAnimations: json['enableAnimations'] ?? true,
      theme: json['theme'] ?? 'default',
      customSettings: Map<String, dynamic>.from(json['customSettings'] ?? {}),
    );
  }
}

/// نموذج تخطيط لوحة التحكم
class DashboardLayout {
  final String id;
  final String name;
  final List<ChartCardModel> cards;
  final int columns;
  final double cardSpacing;
  final bool isDefault;

  const DashboardLayout({
    required this.id,
    required this.name,
    required this.cards,
    this.columns = 2,
    this.cardSpacing = 16.0,
    this.isDefault = false,
  });

  DashboardLayout copyWith({
    String? id,
    String? name,
    List<ChartCardModel>? cards,
    int? columns,
    double? cardSpacing,
    bool? isDefault,
  }) {
    return DashboardLayout(
      id: id ?? this.id,
      name: name ?? this.name,
      cards: cards ?? this.cards,
      columns: columns ?? this.columns,
      cardSpacing: cardSpacing ?? this.cardSpacing,
      isDefault: isDefault ?? this.isDefault,
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'cards': cards.map((card) => card.toJson()).toList(),
      'columns': columns,
      'cardSpacing': cardSpacing,
      'isDefault': isDefault,
    };
  }

  /// إنشاء من JSON
  factory DashboardLayout.fromJson(Map<String, dynamic> json) {
    return DashboardLayout(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      cards: (json['cards'] as List<dynamic>?)
          ?.map((cardJson) => ChartCardModel.fromJson(cardJson))
          .toList() ?? [],
      columns: json['columns'] ?? 2,
      cardSpacing: (json['cardSpacing'] ?? 16.0).toDouble(),
      isDefault: json['isDefault'] ?? false,
    );
  }
}

/// نموذج إحصائيات المهام
class TaskStatistics {
  final int totalTasks;
  final int completedTasks;
  final int pendingTasks;
  final int inProgressTasks;
  final int overdueTasks;
  final Map<String, int> tasksByStatus;
  final Map<String, int> tasksByPriority;
  final Map<String, int> tasksByDepartment;
  final Map<String, int> tasksByAssignee;
  final Map<String, int> tasksByCreator;

  const TaskStatistics({
    required this.totalTasks,
    required this.completedTasks,
    required this.pendingTasks,
    required this.inProgressTasks,
    required this.overdueTasks,
    required this.tasksByStatus,
    required this.tasksByPriority,
    required this.tasksByDepartment,
    required this.tasksByAssignee,
    required this.tasksByCreator,
  });

  factory TaskStatistics.empty() {
    return const TaskStatistics(
      totalTasks: 0,
      completedTasks: 0,
      pendingTasks: 0,
      inProgressTasks: 0,
      overdueTasks: 0,
      tasksByStatus: {},
      tasksByPriority: {},
      tasksByDepartment: {},
      tasksByAssignee: {},
      tasksByCreator: {},
    );
  }
}

/// نموذج إحصائيات المستخدمين
class UserStatistics {
  final int totalUsers;
  final int activeUsers;
  final int inactiveUsers;
  final Map<String, int> usersByDepartment;
  final Map<String, int> usersByRole;
  final Map<String, int> taskAccessByUser;

  const UserStatistics({
    required this.totalUsers,
    required this.activeUsers,
    required this.inactiveUsers,
    required this.usersByDepartment,
    required this.usersByRole,
    required this.taskAccessByUser,
  });

  factory UserStatistics.empty() {
    return const UserStatistics(
      totalUsers: 0,
      activeUsers: 0,
      inactiveUsers: 0,
      usersByDepartment: {},
      usersByRole: {},
      taskAccessByUser: {},
    );
  }
}
