import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../controllers/task_type_controller.dart';
import '../../../models/task_type_models.dart';
import '../../../services/unified_permission_service.dart';

import 'task_type_form_dialog.dart';

/// شاشة إدارة أنواع المهام
class TaskTypeManagementScreen extends StatefulWidget {
  const TaskTypeManagementScreen({super.key});

  @override
  State<TaskTypeManagementScreen> createState() => _TaskTypeManagementScreenState();
}

class _TaskTypeManagementScreenState extends State<TaskTypeManagementScreen> {
  final TaskTypeController _controller = Get.find<TaskTypeController>();
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    await _controller.loadTaskTypes();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة أنواع المهام'),
        backgroundColor: Colors.blue.shade700,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          if (_permissionService.canManageTaskTypes())
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: _showAddDialog,
              tooltip: 'إضافة نوع مهمة جديد',
            ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.blue.shade50,
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'البحث في أنواع المهام...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.white,
              ),
              onChanged: (value) {
                _controller.searchTypes(value);
              },
            ),
          ),
          
          // قائمة أنواع المهام
          Expanded(
            child: Obx(() {
              if (_controller.isLoading.value) {
                return const Center(
                  child: CircularProgressIndicator(),
                );
              }

              if (_controller.error.isNotEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Colors.red.shade300,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        _controller.error,
                        style: TextStyle(
                          color: Colors.red.shade700,
                          fontSize: 16,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadData,
                        child: const Text('إعادة المحاولة'),
                      ),
                    ],
                  ),
                );
              }

              final taskTypes = _controller.filteredTypes;

              if (taskTypes.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.category_outlined,
                        size: 64,
                        color: Colors.grey.shade400,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'لا توجد أنواع مهام',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'ابدأ بإضافة نوع مهمة جديد',
                        style: TextStyle(
                          color: Colors.grey.shade500,
                          fontSize: 14,
                        ),
                      ),
                      if (_permissionService.canManageTaskTypes()) ...[
                        const SizedBox(height: 16),
                        ElevatedButton.icon(
                          onPressed: _showAddDialog,
                          icon: const Icon(Icons.add),
                          label: const Text('إضافة نوع مهمة'),
                        ),
                      ],
                    ],
                  ),
                );
              }

              return ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: taskTypes.length,
                itemBuilder: (context, index) {
                  final taskType = taskTypes[index];
                  return _buildTaskTypeCard(taskType);
                },
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildTaskTypeCard(TaskType taskType) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: taskType.color != null 
                ? _parseColor(taskType.color!) 
                : Colors.blue.shade100,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            taskType.icon != null 
                ? _parseIcon(taskType.icon!) 
                : Icons.category,
            color: taskType.color != null 
                ? Colors.white 
                : Colors.blue.shade700,
          ),
        ),
        title: Text(
          taskType.name,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (taskType.description != null) ...[
              const SizedBox(height: 4),
              Text(
                taskType.description!,
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 14,
                ),
              ),
            ],
            const SizedBox(height: 8),
            Row(
              children: [
                if (taskType.isDefault)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.green.shade100,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      'افتراضي',
                      style: TextStyle(
                        color: Colors.green.shade700,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                const Spacer(),
                Text(
                  'تم الإنشاء: ${_formatDate(taskType.createdAtDateTime)}',
                  style: TextStyle(
                    color: Colors.grey.shade500,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: _permissionService.canManageTaskTypes()
            ? PopupMenuButton<String>(
                onSelected: (value) => _handleMenuAction(value, taskType),
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'edit',
                    child: ListTile(
                      leading: Icon(Icons.edit),
                      title: Text('تعديل'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  if (!taskType.isDefault)
                    const PopupMenuItem(
                      value: 'set_default',
                      child: ListTile(
                        leading: Icon(Icons.star),
                        title: Text('تعيين كافتراضي'),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: ListTile(
                      leading: Icon(Icons.delete, color: Colors.red),
                      title: Text('حذف', style: TextStyle(color: Colors.red)),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                ],
              )
            : null,
      ),
    );
  }

  void _handleMenuAction(String action, TaskType taskType) {
    switch (action) {
      case 'edit':
        _showEditDialog(taskType);
        break;
      case 'set_default':
        _setAsDefault(taskType);
        break;
      case 'delete':
        _showDeleteDialog(taskType);
        break;
    }
  }

  void _showAddDialog() {
    showDialog(
      context: context,
      builder: (context) => TaskTypeFormDialog(
        onSaved: () {
          _loadData();
        },
      ),
    );
  }

  void _showEditDialog(TaskType taskType) {
    showDialog(
      context: context,
      builder: (context) => TaskTypeFormDialog(
        taskType: taskType,
        onSaved: () {
          _loadData();
        },
      ),
    );
  }

  void _setAsDefault(TaskType taskType) async {
    try {
      await _controller.setAsDefault(taskType.id);
      _loadData();
      Get.snackbar(
        'نجح',
        'تم تعيين نوع المهمة كافتراضي',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل في تعيين نوع المهمة كافتراضي: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  void _showDeleteDialog(TaskType taskType) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف نوع المهمة "${taskType.name}"؟\n\nلن يمكن التراجع عن هذا الإجراء.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _deleteTaskType(taskType);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _deleteTaskType(TaskType taskType) async {
    try {
      await _controller.deleteType(taskType.id);
      _loadData();
      Get.snackbar(
        'نجح',
        'تم حذف نوع المهمة بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل في حذف نوع المهمة: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Color _parseColor(String colorString) {
    try {
      if (colorString.startsWith('#')) {
        return Color(int.parse(colorString.substring(1), radix: 16) + 0xFF000000);
      }
      return Colors.blue;
    } catch (e) {
      return Colors.blue;
    }
  }

  IconData _parseIcon(String iconString) {
    switch (iconString.toLowerCase()) {
      case 'code':
        return Icons.code;
      case 'design_services':
        return Icons.design_services;
      case 'bug_report':
        return Icons.bug_report;
      case 'description':
        return Icons.description;
      case 'build':
        return Icons.build;
      case 'analytics':
        return Icons.analytics;
      default:
        return Icons.category;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
