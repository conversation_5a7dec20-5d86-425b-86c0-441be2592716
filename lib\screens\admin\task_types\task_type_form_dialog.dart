import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../controllers/task_type_controller.dart';
import '../../../models/task_type_models.dart';


/// حوار إضافة/تعديل نوع المهمة
class TaskTypeFormDialog extends StatefulWidget {
  final TaskType? taskType;
  final VoidCallback? onSaved;

  const TaskTypeFormDialog({
    super.key,
    this.taskType,
    this.onSaved,
  });

  @override
  State<TaskTypeFormDialog> createState() => _TaskTypeFormDialogState();
}

class _TaskTypeFormDialogState extends State<TaskTypeFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final TaskTypeController _controller = Get.find<TaskTypeController>();

  late TextEditingController _nameController;
  late TextEditingController _descriptionController;
  late TextEditingController _colorController;
  
  String _selectedIcon = 'category';
  bool _isDefault = false;
  bool _isLoading = false;

  final List<Map<String, dynamic>> _availableIcons = [
    {'name': 'category', 'icon': Icons.category, 'label': 'عام'},
    {'name': 'code', 'icon': Icons.code, 'label': 'برمجة'},
    {'name': 'design_services', 'icon': Icons.design_services, 'label': 'تصميم'},
    {'name': 'bug_report', 'icon': Icons.bug_report, 'label': 'اختبار'},
    {'name': 'description', 'icon': Icons.description, 'label': 'توثيق'},
    {'name': 'build', 'icon': Icons.build, 'label': 'صيانة'},
    {'name': 'analytics', 'icon': Icons.analytics, 'label': 'تحليل'},
    {'name': 'support', 'icon': Icons.support, 'label': 'دعم فني'},
    {'name': 'meeting_room', 'icon': Icons.meeting_room, 'label': 'اجتماع'},
    {'name': 'school', 'icon': Icons.school, 'label': 'تدريب'},
  ];

  final List<Color> _availableColors = [
    Colors.blue,
    Colors.green,
    Colors.orange,
    Colors.purple,
    Colors.red,
    Colors.teal,
    Colors.indigo,
    Colors.pink,
    Colors.amber,
    Colors.cyan,
  ];

  Color _selectedColor = Colors.blue;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  void _initializeControllers() {
    _nameController = TextEditingController(text: widget.taskType?.name ?? '');
    _descriptionController = TextEditingController(text: widget.taskType?.description ?? '');
    _colorController = TextEditingController(text: widget.taskType?.color ?? '#2196F3');
    
    if (widget.taskType != null) {
      _selectedIcon = widget.taskType!.icon ?? 'category';
      _isDefault = widget.taskType!.isDefault;
      _selectedColor = _parseColor(widget.taskType!.color ?? '#2196F3');
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _colorController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: const BoxConstraints(maxWidth: 500),
        padding: const EdgeInsets.all(24),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // العنوان
              Row(
                children: [
                  Icon(
                    widget.taskType == null ? Icons.add : Icons.edit,
                    color: Colors.blue.shade700,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    widget.taskType == null ? 'إضافة نوع مهمة جديد' : 'تعديل نوع المهمة',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // اسم نوع المهمة
              TextFormField(
                controller: _nameController,
                decoration: InputDecoration(
                  labelText: 'اسم نوع المهمة',
                  hintText: 'أدخل اسم نوع المهمة',
                  prefixIcon: const Icon(Icons.label),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'اسم نوع المهمة مطلوب';
                  }
                  if (value.trim().length < 2) {
                    return 'اسم نوع المهمة يجب أن يكون أكثر من حرفين';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // الوصف
              TextFormField(
                controller: _descriptionController,
                decoration: InputDecoration(
                  labelText: 'الوصف (اختياري)',
                  hintText: 'أدخل وصف نوع المهمة',
                  prefixIcon: const Icon(Icons.description),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 16),

              // اختيار الأيقونة
              Text(
                'الأيقونة',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey.shade700,
                ),
              ),
              const SizedBox(height: 8),
              SizedBox(
                height: 60,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: _availableIcons.length,
                  itemBuilder: (context, index) {
                    final iconData = _availableIcons[index];
                    final isSelected = _selectedIcon == iconData['name'];
                    
                    return GestureDetector(
                      onTap: () {
                        setState(() {
                          _selectedIcon = iconData['name'];
                        });
                      },
                      child: Container(
                        width: 60,
                        margin: const EdgeInsets.only(left: 8),
                        decoration: BoxDecoration(
                          color: isSelected ? _selectedColor : Colors.grey.shade100,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: isSelected ? _selectedColor : Colors.grey.shade300,
                            width: 2,
                          ),
                        ),
                        child: Icon(
                          iconData['icon'],
                          color: isSelected ? Colors.white : Colors.grey.shade600,
                          size: 24,
                        ),
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(height: 16),

              // اختيار اللون
              Text(
                'اللون',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey.shade700,
                ),
              ),
              const SizedBox(height: 8),
              SizedBox(
                height: 50,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: _availableColors.length,
                  itemBuilder: (context, index) {
                    final color = _availableColors[index];
                    final isSelected = _selectedColor == color;
                    
                    return GestureDetector(
                      onTap: () {
                        setState(() {
                          _selectedColor = color;
                          _colorController.text = '#${color.value.toRadixString(16).padLeft(8, '0').substring(2).toUpperCase()}';
                        });
                      },
                      child: Container(
                        width: 50,
                        margin: const EdgeInsets.only(left: 8),
                        decoration: BoxDecoration(
                          color: color,
                          borderRadius: BorderRadius.circular(25),
                          border: Border.all(
                            color: isSelected ? Colors.black : Colors.grey.shade300,
                            width: isSelected ? 3 : 1,
                          ),
                        ),
                        child: isSelected
                            ? const Icon(
                                Icons.check,
                                color: Colors.white,
                                size: 20,
                              )
                            : null,
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(height: 16),

              // تعيين كافتراضي
              SwitchListTile(
                title: const Text('تعيين كنوع افتراضي'),
                subtitle: const Text('سيتم استخدام هذا النوع افتراضياً عند إنشاء مهام جديدة'),
                value: _isDefault,
                onChanged: (value) {
                  setState(() {
                    _isDefault = value;
                  });
                },
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              const SizedBox(height: 24),

              // أزرار الإجراءات
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
                      child: const Text('إلغاء'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _saveTaskType,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue.shade700,
                        foregroundColor: Colors.white,
                      ),
                      child: _isLoading
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : Text(widget.taskType == null ? 'إضافة' : 'حفظ'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _saveTaskType() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final name = _nameController.text.trim();
      final description = _descriptionController.text.trim().isEmpty 
          ? null 
          : _descriptionController.text.trim();
      final color = '#${_selectedColor.value.toRadixString(16).padLeft(8, '0').substring(2).toUpperCase()}';

      if (widget.taskType == null) {
        // إضافة نوع مهمة جديد
        await _controller.createType(
          name: name,
          description: description,
          color: color,
          icon: _selectedIcon,
          isDefault: _isDefault,
        );
        
        Get.snackbar(
          'نجح',
          'تم إضافة نوع المهمة بنجاح',
          backgroundColor: Colors.green,
          colorText: Colors.white,
          duration: const Duration(seconds: 2),
        );
      } else {
        // تعديل نوع مهمة موجود
        final updatedType = widget.taskType!.copyWith(
          name: name,
          description: description,
          color: color,
          icon: _selectedIcon,
          isDefault: _isDefault,
        );
        
        await _controller.updateType(updatedType);

        Get.snackbar(
          'نجح',
          'تم تحديث نوع المهمة بنجاح',
          backgroundColor: Colors.green,
          colorText: Colors.white,
          duration: const Duration(seconds: 2),
        );
      }

      if (mounted) {
        Navigator.of(context).pop();
        widget.onSaved?.call();
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل في حفظ نوع المهمة: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Color _parseColor(String colorString) {
    try {
      if (colorString.startsWith('#')) {
        return Color(int.parse(colorString.substring(1), radix: 16) + 0xFF000000);
      }
      return Colors.blue;
    } catch (e) {
      return Colors.blue;
    }
  }
}
