import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/dashboard_models.dart';
import 'chart_card_widget.dart';

/// Widget بطاقة مخطط قابلة لتغيير الحجم
class ResizableChartCard extends StatefulWidget {
  final ChartCardModel card;
  final Function(ChartCardModel) onCardUpdated;
  final Function()? onRefresh;
  final Function(String)? onRefreshSingle;
  final Function(ChartType)? onChartTypeChanged;
  final Function(Size)? onSizeChanged;
  final double minWidth;
  final double minHeight;
  final double maxWidth;
  final double maxHeight;

  const ResizableChartCard({
    super.key,
    required this.card,
    required this.onCardUpdated,
    this.onRefresh,
    this.onRefreshSingle,
    this.onChartTypeChanged,
    this.onSizeChanged,
    this.minWidth = 200,
    this.minHeight = 150,
    this.maxWidth = double.infinity,
    this.maxHeight = double.infinity,
  });

  @override
  State<ResizableChartCard> createState() => _ResizableChartCardState();
}

class _ResizableChartCardState extends State<ResizableChartCard> {
  late Size _currentSize;
  bool _isResizing = false;

  @override
  void initState() {
    super.initState();
    _currentSize = widget.card.size;
    _loadSavedSize();
  }

  /// تحميل الحجم المحفوظ
  Future<void> _loadSavedSize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = 'card_size_${widget.card.id}';
      final sizeData = prefs.getString(key);

      if (sizeData != null) {
        final parts = sizeData.split(',');
        if (parts.length == 2) {
          final width = double.tryParse(parts[0]);
          final height = double.tryParse(parts[1]);

          if (width != null && height != null) {
            setState(() {
              _currentSize = Size(width, height);
            });
            debugPrint('✅ تم تحميل حجم محفوظ للبطاقة ${widget.card.id}: ${width}x$height');
          }
        }
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل حجم البطاقة: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: _currentSize.width,
      height: _currentSize.height,
      child: Stack(
        children: [
          // البطاقة الأساسية
          Positioned.fill(
            child: ChartCardWidget(
              card: widget.card.copyWith(size: _currentSize),
              onRefresh: widget.onRefresh,
              onRefreshSingle: widget.onRefreshSingle,
              onChartTypeChanged: (newType) {
                // تحديث نوع المخطط في البطاقة
                final updatedCard = widget.card.copyWith(chartType: newType);
                widget.onCardUpdated(updatedCard);

                // استدعاء callback إضافي إذا كان متوفراً
                widget.onChartTypeChanged?.call(newType);
              },
              onSizeChanged: (newSize) {
                setState(() {
                  _currentSize = newSize;
                });

                // تحديث حجم البطاقة
                final updatedCard = widget.card.copyWith(size: newSize);
                widget.onCardUpdated(updatedCard);

                // استدعاء callback إضافي إذا كان متوفراً
                widget.onSizeChanged?.call(newSize);
              },
            ),
          ),
          
          // مقابض تغيير الحجم
          if (widget.card.isResizable) ..._buildResizeHandles(),
          
          // مؤشر تغيير الحجم
          if (_isResizing) _buildResizeIndicator(),
        ],
      ),
    );
  }

  /// بناء مقابض تغيير الحجم
  List<Widget> _buildResizeHandles() {
    return [
      // مقبض الزاوية اليمنى السفلى
      Positioned(
        right: 0,
        bottom: 0,
        child: _buildCornerHandle(
          onPanUpdate: (details) => _handleCornerResize(details),
          cursor: SystemMouseCursors.resizeDownRight,
        ),
      ),
      
      // مقبض الحافة اليمنى
      Positioned(
        right: 0,
        top: 20,
        bottom: 20,
        child: _buildEdgeHandle(
          onPanUpdate: (details) => _handleRightEdgeResize(details),
          cursor: SystemMouseCursors.resizeLeftRight,
          isVertical: true,
        ),
      ),
      
      // مقبض الحافة السفلى
      Positioned(
        left: 20,
        right: 20,
        bottom: 0,
        child: _buildEdgeHandle(
          onPanUpdate: (details) => _handleBottomEdgeResize(details),
          cursor: SystemMouseCursors.resizeUpDown,
          isVertical: false,
        ),
      ),
    ];
  }

  /// بناء مقبض الزاوية
  Widget _buildCornerHandle({
    required Function(DragUpdateDetails) onPanUpdate,
    required SystemMouseCursor cursor,
  }) {
    return MouseRegion(
      cursor: cursor,
      child: GestureDetector(
        onPanStart: (_) => setState(() => _isResizing = true),
        onPanEnd: (_) => _finishResize(),
        onPanUpdate: onPanUpdate,
        child: Container(
          width: 20,
          height: 20,
          decoration: BoxDecoration(
            color: Colors.blue.withValues(alpha: 0.7),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(8),
            ),
          ),
          child: const Icon(
            Icons.drag_handle,
            size: 12,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  /// بناء مقبض الحافة
  Widget _buildEdgeHandle({
    required Function(DragUpdateDetails) onPanUpdate,
    required SystemMouseCursor cursor,
    required bool isVertical,
  }) {
    return MouseRegion(
      cursor: cursor,
      child: GestureDetector(
        onPanStart: (_) => setState(() => _isResizing = true),
        onPanEnd: (_) => _finishResize(),
        onPanUpdate: onPanUpdate,
        child: Container(
          width: isVertical ? 8 : null,
          height: isVertical ? null : 8,
          decoration: BoxDecoration(
            color: Colors.blue.withValues(alpha: 0.5),
            borderRadius: BorderRadius.circular(4),
          ),
        ),
      ),
    );
  }

  /// بناء مؤشر تغيير الحجم
  Widget _buildResizeIndicator() {
    return Positioned(
      top: 8,
      left: 8,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.7),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Text(
          '${_currentSize.width.toInt()} × ${_currentSize.height.toInt()}',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  /// معالجة تغيير حجم الزاوية مع تحسينات الأداء
  void _handleCornerResize(DragUpdateDetails details) {
    setState(() {
      // حساب الأبعاد الجديدة مع تطبيق الحد الأدنى فقط
      final newWidth = (_currentSize.width + details.delta.dx)
          .clamp(widget.minWidth, double.infinity);
      final newHeight = (_currentSize.height + details.delta.dy)
          .clamp(widget.minHeight, double.infinity);

      // التأكد من أن الأبعاد موجبة ومعقولة
      if (newWidth <= 0 || newHeight <= 0) {
        debugPrint('⚠️ تم تجاهل حجم غير صالح: ${newWidth}x$newHeight');
        return;
      }

      _currentSize = Size(newWidth, newHeight);

      debugPrint('🔄 تحجيم الزاوية: ${newWidth.toInt()}x${newHeight.toInt()}');
    });

    // إضافة ردود فعل لمسية
    HapticFeedback.selectionClick();
  }

  /// معالجة تغيير حجم الحافة اليمنى مع تحسينات
  void _handleRightEdgeResize(DragUpdateDetails details) {
    setState(() {
      // تطبيق الحد الأدنى فقط للعرض
      final newWidth = (_currentSize.width + details.delta.dx)
          .clamp(widget.minWidth, double.infinity);

      // التأكد من أن العرض موجب
      if (newWidth <= 0) {
        debugPrint('⚠️ تم تجاهل عرض غير صالح: $newWidth');
        return;
      }

      _currentSize = Size(newWidth, _currentSize.height);

      debugPrint('↔️ تحجيم العرض: ${newWidth.toInt()}px');
    });

    // إضافة ردود فعل لمسية خفيفة
    HapticFeedback.lightImpact();
  }

  /// معالجة تغيير حجم الحافة السفلى مع تحسينات
  void _handleBottomEdgeResize(DragUpdateDetails details) {
    setState(() {
      // تطبيق الحد الأدنى فقط للارتفاع
      final newHeight = (_currentSize.height + details.delta.dy)
          .clamp(widget.minHeight, double.infinity);

      // التأكد من أن الارتفاع موجب
      if (newHeight <= 0) {
        debugPrint('⚠️ تم تجاهل ارتفاع غير صالح: $newHeight');
        return;
      }

      _currentSize = Size(_currentSize.width, newHeight);

      debugPrint('↕️ تحجيم الارتفاع: ${newHeight.toInt()}px');
    });

    // إضافة ردود فعل لمسية خفيفة
    HapticFeedback.lightImpact();
  }

  /// إنهاء تغيير الحجم مع حفظ التفضيلات
  void _finishResize() {
    setState(() => _isResizing = false);

    // تحديث البطاقة بالحجم الجديد
    final updatedCard = widget.card.copyWith(size: _currentSize);
    widget.onCardUpdated(updatedCard);

    // إضافة ردود فعل لمسية للتأكيد
    HapticFeedback.mediumImpact();

    // حفظ الحجم في التفضيلات المحلية
    _saveCardSizePreference();
  }

  /// حفظ تفضيلات حجم البطاقة
  Future<void> _saveCardSizePreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = 'card_size_${widget.card.id}';
      final sizeData = '${_currentSize.width},${_currentSize.height}';
      await prefs.setString(key, sizeData);
      debugPrint('✅ تم حفظ حجم البطاقة ${widget.card.id}: ${_currentSize.width}x${_currentSize.height}');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ حجم البطاقة: $e');
    }
  }
}

/// Widget مخصص لعرض أحجام محددة مسبقاً
class SizePresetWidget extends StatelessWidget {
  final Size currentSize;
  final Function(Size) onSizeChanged;

  const SizePresetWidget({
    super.key,
    required this.currentSize,
    required this.onSizeChanged,
  });

  static const List<MapEntry<String, Size>> _presetSizes = [
    MapEntry('صغير', Size(300, 200)),
    MapEntry('متوسط', Size(400, 300)),
    MapEntry('كبير', Size(600, 400)),
    MapEntry('عريض', Size(800, 300)),
    MapEntry('طويل', Size(400, 500)),
  ];

  @override
  Widget build(BuildContext context) {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: _presetSizes.map((preset) {
        final isSelected = preset.value == currentSize;
        
        return GestureDetector(
          onTap: () => onSizeChanged(preset.value),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: isSelected ? Colors.blue : Colors.grey[200],
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: isSelected ? Colors.blue : Colors.grey[400]!,
              ),
            ),
            child: Text(
              '${preset.key}\n${preset.value.width.toInt()}×${preset.value.height.toInt()}',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: isSelected ? Colors.white : Colors.black87,
                fontSize: 12,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }
}
