import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import '../../config/api_config.dart';
import '../../models/task_models.dart';
import '../models/dashboard_models.dart';
import '../config/dashboard_config.dart';

/// خدمة جلب بيانات لوحة التحكم من API
class DashboardDataService {
  static final String _baseUrl = ApiConfig.baseUrl;

  /// بناء معاملات الاستعلام من الفلاتر
  Map<String, String> _buildQueryParameters(Map<String, dynamic> filters) {
    final queryParams = <String, String>{};

    for (final entry in filters.entries) {
      final key = entry.key;
      final value = entry.value;

      if (value != null) {
        if (value is List) {
          // للقوائم المتعددة مثل الحالات والأولويات
          if (value.isNotEmpty) {
            queryParams[key] = value.join(',');
          }
        } else if (value is DateTimeRange) {
          // لنطاق التاريخ - تحديد نوع التاريخ المطلوب
          if (key == 'created_date_range') {
            queryParams['created_at_start'] = (value.start.millisecondsSinceEpoch ~/ 1000).toString();
            queryParams['created_at_end'] = (value.end.millisecondsSinceEpoch ~/ 1000).toString();
          } else if (key == 'start_date_range') {
            queryParams['start_date_start'] = (value.start.millisecondsSinceEpoch ~/ 1000).toString();
            queryParams['start_date_end'] = (value.end.millisecondsSinceEpoch ~/ 1000).toString();
          } else if (key == 'due_date_range') {
            queryParams['due_date_start'] = (value.start.millisecondsSinceEpoch ~/ 1000).toString();
            queryParams['due_date_end'] = (value.end.millisecondsSinceEpoch ~/ 1000).toString();
          } else if (key == 'completed_date_range') {
            queryParams['completed_at_start'] = (value.start.millisecondsSinceEpoch ~/ 1000).toString();
            queryParams['completed_at_end'] = (value.end.millisecondsSinceEpoch ~/ 1000).toString();
          } else {
            // للتوافق مع الإصدارات القديمة
            queryParams['${key}_start'] = value.start.toIso8601String();
            queryParams['${key}_end'] = value.end.toIso8601String();
          }
        } else {
          // للقيم المفردة
          queryParams[key] = value.toString();
        }
      }
    }

    debugPrint('🔍 معاملات الاستعلام: $queryParams');
    return queryParams;
  }

  /// جلب إحصائيات المهام الشاملة
  Future<TaskStatistics> getTaskStatistics({Map<String, dynamic>? filters}) async {
    try {
      final uri = Uri.parse('$_baseUrl/api/Dashboard/task-statistics');
      final response = await http.get(
        uri,
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return _parseTaskStatistics(data);
      } else {
        throw Exception('فشل في جلب إحصائيات المهام: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('خطأ في جلب إحصائيات المهام: $e');
      return TaskStatistics.empty();
    }
  }

  /// جلب إحصائيات المستخدمين
  Future<UserStatistics> getUserStatistics({Map<String, dynamic>? filters}) async {
    try {
      final uri = Uri.parse('$_baseUrl/api/Dashboard/user-statistics');
      final response = await http.get(
        uri,
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return _parseUserStatistics(data);
      } else {
        throw Exception('فشل في جلب إحصائيات المستخدمين: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('خطأ في جلب إحصائيات المستخدمين: $e');
      return UserStatistics.empty();
    }
  }

  /// جلب بيانات المهام حسب الحالة
  Future<List<ChartDataPoint>> getTasksByStatus({Map<String, dynamic>? filters}) async {
    try {
      // بناء URL مع الفلاتر
      var uri = Uri.parse('$_baseUrl/api/Tasks');
      if (filters != null && filters.isNotEmpty) {
        uri = uri.replace(queryParameters: _buildQueryParameters(filters));
        debugPrint('🔍 استدعاء API مع فلاتر: ${uri.query}');
      }

      final response = await http.get(
        uri,
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final tasks = (data['data'] as List?)?.map((item) => Task.fromJson(item)).toList() ?? [];

        debugPrint('📊 تم جلب ${tasks.length} مهمة مع الفلاتر');
        return _groupTasksByStatus(tasks);
      } else {
        throw Exception('فشل في جلب المهام: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('خطأ في جلب المهام حسب الحالة: $e');
      return [];
    }
  }

  /// جلب بيانات المهام حسب الأولوية
  Future<List<ChartDataPoint>> getTasksByPriority({Map<String, dynamic>? filters}) async {
    try {
      // بناء URL مع الفلاتر
      var uri = Uri.parse('$_baseUrl/api/Tasks');
      if (filters != null && filters.isNotEmpty) {
        uri = uri.replace(queryParameters: _buildQueryParameters(filters));
        debugPrint('🔍 استدعاء API مع فلاتر: ${uri.query}');
      }

      final response = await http.get(
        uri,
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final tasks = (data['data'] as List?)?.map((item) => Task.fromJson(item)).toList() ?? [];

        debugPrint('📊 تم جلب ${tasks.length} مهمة مع الفلاتر');
        return _groupTasksByPriority(tasks);
      } else {
        throw Exception('فشل في جلب المهام: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('خطأ في جلب المهام حسب الأولوية: $e');
      return [];
    }
  }

  /// جلب بيانات المهام حسب المنشئ (creator_id)
  Future<List<ChartDataPoint>> getTasksByCreator({Map<String, dynamic>? filters}) async {
    try {
      // بناء URL مع الفلاتر
      var uri = Uri.parse('$_baseUrl/api/Tasks');
      if (filters != null && filters.isNotEmpty) {
        uri = uri.replace(queryParameters: _buildQueryParameters(filters));
        debugPrint('🔍 استدعاء API مع فلاتر: ${uri.query}');
      }

      final response = await http.get(
        uri,
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final tasks = (data['data'] as List?)?.map((item) => Task.fromJson(item)).toList() ?? [];

        debugPrint('📊 تم جلب ${tasks.length} مهمة مع الفلاتر');
        return await _groupTasksByCreator(tasks);
      } else {
        throw Exception('فشل في جلب المهام: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('خطأ في جلب المهام حسب المنشئ: $e');
      return [];
    }
  }

  /// جلب بيانات المهام حسب المكلف (assignee)
  Future<List<ChartDataPoint>> getTasksByAssignee({Map<String, dynamic>? filters}) async {
    try {
      final uri = Uri.parse('$_baseUrl/api/Tasks');
      final response = await http.get(
        uri,
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final tasks = (data['data'] as List?)?.map((item) => Task.fromJson(item)).toList() ?? [];
        
        return await _groupTasksByAssignee(tasks);
      } else {
        throw Exception('فشل في جلب المهام: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('خطأ في جلب المهام حسب المكلف: $e');
      return [];
    }
  }

  /// جلب بيانات المهام حسب الأقسام
  Future<List<ChartDataPoint>> getTasksByDepartment({Map<String, dynamic>? filters}) async {
    try {
      // بناء URL مع الفلاتر
      var uri = Uri.parse('$_baseUrl/api/Tasks');
      if (filters != null && filters.isNotEmpty) {
        uri = uri.replace(queryParameters: _buildQueryParameters(filters));
        debugPrint('🔍 استدعاء API مع فلاتر: ${uri.query}');
      }

      final response = await http.get(
        uri,
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final tasks = (data['data'] as List?)?.map((item) => Task.fromJson(item)).toList() ?? [];

        debugPrint('📊 تم جلب ${tasks.length} مهمة مع الفلاتر');
        return await _groupTasksByDepartment(tasks);
      } else {
        throw Exception('فشل في جلب المهام: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('خطأ في جلب المهام حسب الأقسام: $e');
      return [];
    }
  }

  /// جلب بيانات المهام حسب المستخدمين والوصول (task_access_users)
  Future<List<ChartDataPoint>> getTasksByUserAccess({Map<String, dynamic>? filters}) async {
    try {
      final uri = Uri.parse('$_baseUrl/api/Dashboard/task-access-statistics');
      final response = await http.get(
        uri,
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return _parseTaskAccessData(data);
      } else {
        throw Exception('فشل في جلب بيانات الوصول للمهام: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('خطأ في جلب بيانات الوصول للمهام: $e');
      return [];
    }
  }

  /// جلب بيانات أداء الأقسام
  Future<List<ChartDataPoint>> getDepartmentPerformance({Map<String, dynamic>? filters}) async {
    try {
      final uri = Uri.parse('$_baseUrl/api/Dashboard/department-performance');
      final response = await http.get(
        uri,
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final departments = data as List<dynamic>;

        return departments.map((dept) => ChartDataPoint(
          label: dept['department'] ?? 'قسم غير محدد',
          value: (dept['completionRate'] ?? 0).toDouble(),
          category: 'department_performance',
          color: DashboardConfig.getColorForValue('department', dept['department'] ?? 'قسم غير محدد'),
          metadata: {
            'totalTasks': dept['totalTasks'],
            'completedTasks': dept['completedTasks'],
            'inProgressTasks': dept['inProgressTasks'],
            'pendingTasks': dept['pendingTasks'],
            'overdueTasks': dept['overdueTasks'],
          },
        )).toList();
      } else {
        throw Exception('فشل في جلب بيانات أداء الأقسام: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('خطأ في جلب بيانات أداء الأقسام: $e');
      return [];
    }
  }

  /// جلب بيانات مزدوجة الأعمدة: المستخدم × حالات المهام
  Future<List<Map<String, dynamic>>> getUserTaskStatusMatrix({Map<String, dynamic>? filters}) async {
    try {
      final uri = Uri.parse('$_baseUrl/api/Dashboard/user-status-matrix');
      final response = await http.get(
        uri,
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return List<Map<String, dynamic>>.from(data);
      } else {
        throw Exception('فشل في جلب مصفوفة المستخدم والحالة: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('خطأ في جلب مصفوفة المستخدم والحالة: $e');
      return [];
    }
  }

  /// جلب بيانات التحليل الزمني للمهام
  Future<List<ChartDataPoint>> getTaskTrends({Map<String, dynamic>? filters}) async {
    try {
      // يمكن إضافة endpoint مخصص للتحليل الزمني لاحقاً
      // حالياً سنستخدم البيانات الموجودة
      final tasks = await _getAllTasks();
      return _generateTaskTrends(tasks);
    } catch (e) {
      debugPrint('خطأ في جلب بيانات التحليل الزمني: $e');
      return [];
    }
  }

  /// جلب جميع المهام للتحليل المحلي
  Future<List<Task>> _getAllTasks() async {
    final uri = Uri.parse('$_baseUrl/api/Tasks');
    final response = await http.get(
      uri,
      headers: {'Content-Type': 'application/json'},
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      final tasks = (data['data'] as List?)?.map((item) => Task.fromJson(item)).toList() ?? [];
      return tasks.where((task) => !task.isDeleted).toList();
    } else {
      throw Exception('فشل في جلب المهام: ${response.statusCode}');
    }
  }

  /// توليد بيانات التحليل الزمني
  List<ChartDataPoint> _generateTaskTrends(List<Task> tasks) {
    final Map<String, int> monthlyTasks = {};

    for (final task in tasks) {
      final date = DateTime.fromMillisecondsSinceEpoch(task.createdAt * 1000);
      final monthKey = '${date.year}-${date.month.toString().padLeft(2, '0')}';
      monthlyTasks[monthKey] = (monthlyTasks[monthKey] ?? 0) + 1;
    }

    final colors = [Colors.blue, Colors.green, Colors.orange, Colors.purple, Colors.teal];
    int colorIndex = 0;

    return monthlyTasks.entries.map((entry) => ChartDataPoint(
      label: entry.key,
      value: entry.value.toDouble(),
      category: 'monthly_trend',
      color: colors[colorIndex++ % colors.length],
    )).toList()..sort((a, b) => a.label.compareTo(b.label));
  }

  /// تحليل إحصائيات المهام من البيانات المستلمة
  TaskStatistics _parseTaskStatistics(Map<String, dynamic> data) {
    return TaskStatistics(
      totalTasks: data['totalTasks'] ?? 0,
      completedTasks: data['completedTasks'] ?? 0,
      pendingTasks: data['pendingTasks'] ?? 0,
      inProgressTasks: data['inProgressTasks'] ?? 0,
      overdueTasks: data['overdueTasks'] ?? 0,
      tasksByStatus: Map<String, int>.from(data['tasksByStatus'] ?? {}),
      tasksByPriority: Map<String, int>.from(data['tasksByPriority'] ?? {}),
      tasksByDepartment: Map<String, int>.from(data['tasksByDepartment'] ?? {}),
      tasksByAssignee: Map<String, int>.from(data['tasksByAssignee'] ?? {}),
      tasksByCreator: Map<String, int>.from(data['tasksByCreator'] ?? {}),
    );
  }

  /// تحليل إحصائيات المستخدمين من البيانات المستلمة
  UserStatistics _parseUserStatistics(Map<String, dynamic> data) {
    return UserStatistics(
      totalUsers: data['totalUsers'] ?? 0,
      activeUsers: data['activeUsers'] ?? 0,
      inactiveUsers: data['inactiveUsers'] ?? 0,
      usersByDepartment: Map<String, int>.from(data['usersByDepartment'] ?? {}),
      usersByRole: Map<String, int>.from(data['usersByRole'] ?? {}),
      taskAccessByUser: Map<String, int>.from(data['taskAccessByUser'] ?? {}),
    );
  }

  /// تجميع المهام حسب الحالة
  List<ChartDataPoint> _groupTasksByStatus(List<Task> tasks) {
    final statusCounts = <String, int>{};
    
    for (final task in tasks) {
      if (!task.isDeleted) {
        statusCounts[task.status] = (statusCounts[task.status] ?? 0) + 1;
      }
    }

    return statusCounts.entries.map((entry) => ChartDataPoint(
      label: _getStatusDisplayName(entry.key),
      value: entry.value.toDouble(),
      category: 'status',
      color: _getStatusColor(entry.key),
    )).toList();
  }

  /// تجميع المهام حسب الأولوية
  List<ChartDataPoint> _groupTasksByPriority(List<Task> tasks) {
    final priorityCounts = <String, int>{};

    for (final task in tasks) {
      if (!task.isDeleted) {
        priorityCounts[task.priority] = (priorityCounts[task.priority] ?? 0) + 1;
      }
    }

    return priorityCounts.entries.map((entry) => ChartDataPoint(
      label: _getPriorityDisplayName(entry.key),
      value: entry.value.toDouble(),
      category: 'priority',
      color: _getPriorityColor(entry.key),
    )).toList();
  }

  /// تجميع المهام حسب نوع المهمة
  List<ChartDataPoint> _groupTasksByType(List<Task> tasks) {
    final typeCounts = <String, Map<String, dynamic>>{};

    for (final task in tasks) {
      if (!task.isDeleted) {
        String typeName;
        int typeId;

        if (task.taskTypeId == null || task.taskType == null) {
          typeName = 'بدون نوع محدد';
          typeId = 0;
        } else {
          typeName = task.taskType!.name;
          typeId = task.taskTypeId!;
        }

        if (typeCounts.containsKey(typeName)) {
          typeCounts[typeName]!['count'] = typeCounts[typeName]!['count'] + 1;
        } else {
          typeCounts[typeName] = {
            'count': 1,
            'typeId': typeId,
            'typeName': typeName,
          };
        }
      }
    }

    return typeCounts.entries.map((entry) => ChartDataPoint(
      label: entry.key,
      value: entry.value['count'].toDouble(),
      category: 'type',
      color: DashboardConfig.getColorForValue('task_type', entry.value['typeId'].toString()),
      metadata: {
        'taskTypeId': entry.value['typeId'],
        'typeName': entry.value['typeName'],
        'taskCount': entry.value['count'],
      },
    )).toList();
  }

  /// تجميع المهام حسب المنشئ
  Future<List<ChartDataPoint>> _groupTasksByCreator(List<Task> tasks) async {
    final creatorCounts = <int, int>{};
    
    for (final task in tasks) {
      if (!task.isDeleted) {
        creatorCounts[task.creatorId] = (creatorCounts[task.creatorId] ?? 0) + 1;
      }
    }

    final result = <ChartDataPoint>[];
    for (final entry in creatorCounts.entries) {
      final userName = await _getUserName(entry.key);
      result.add(ChartDataPoint(
        label: userName,
        value: entry.value.toDouble(),
        category: 'creator',
        color: DashboardConfig.getColorForValue('contributor', userName),
        metadata: {'userId': entry.key},
      ));
    }

    return result;
  }

  /// تجميع المهام حسب المكلف
  Future<List<ChartDataPoint>> _groupTasksByAssignee(List<Task> tasks) async {
    final assigneeCounts = <int, int>{};
    
    for (final task in tasks) {
      if (!task.isDeleted && task.assigneeId != null) {
        assigneeCounts[task.assigneeId!] = (assigneeCounts[task.assigneeId!] ?? 0) + 1;
      }
    }

    final result = <ChartDataPoint>[];
    for (final entry in assigneeCounts.entries) {
      final userName = await _getUserName(entry.key);
      result.add(ChartDataPoint(
        label: userName,
        value: entry.value.toDouble(),
        category: 'assignee',
        color: DashboardConfig.getColorForValue('assignee', userName),
        metadata: {'userId': entry.key},
      ));
    }

    return result;
  }

  /// تجميع المهام حسب الأقسام
  Future<List<ChartDataPoint>> _groupTasksByDepartment(List<Task> tasks) async {
    final departmentCounts = <int, int>{};
    
    for (final task in tasks) {
      if (!task.isDeleted && task.departmentId != null) {
        departmentCounts[task.departmentId!] = (departmentCounts[task.departmentId!] ?? 0) + 1;
      }
    }

    final result = <ChartDataPoint>[];
    for (final entry in departmentCounts.entries) {
      final departmentName = await _getDepartmentName(entry.key);
      result.add(ChartDataPoint(
        label: departmentName,
        value: entry.value.toDouble(),
        category: 'department',
        color: DashboardConfig.getColorForValue('department', departmentName),
        metadata: {'departmentId': entry.key},
      ));
    }

    return result;
  }

  /// تحليل بيانات الوصول للمهام
  List<ChartDataPoint> _parseTaskAccessData(Map<String, dynamic> data) {
    final accessData = data['userTaskAccess'] as Map<String, dynamic>? ?? {};
    
    return accessData.entries.map((entry) => ChartDataPoint(
      label: entry.key,
      value: (entry.value as num).toDouble(),
      category: 'access',
      color: DashboardConfig.getColorForValue('contributor', entry.key),
    )).toList();
  }

  /// الحصول على اسم المستخدم
  Future<String> _getUserName(int userId) async {
    try {
      final uri = Uri.parse('$_baseUrl/api/Users/<USER>');
      final response = await http.get(
        uri,
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['fullName'] ?? data['username'] ?? 'مستخدم غير معروف';
      }
    } catch (e) {
      debugPrint('خطأ في جلب اسم المستخدم $userId: $e');
    }
    return 'مستخدم $userId';
  }

  /// الحصول على اسم القسم
  Future<String> _getDepartmentName(int departmentId) async {
    try {
      final uri = Uri.parse('$_baseUrl/api/Departments/$departmentId');
      final response = await http.get(
        uri,
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data['name'] ?? 'قسم غير معروف';
      }
    } catch (e) {
      debugPrint('خطأ في جلب اسم القسم $departmentId: $e');
    }
    return 'قسم $departmentId';
  }

  /// الحصول على اسم الحالة للعرض
  String _getStatusDisplayName(String status) {
    const statusNames = {
      'pending': 'قيد الانتظار',
      'in_progress': 'قيد التنفيذ',
      'completed': 'مكتملة',
      'cancelled': 'ملغية',
      'waiting_for_info': 'في انتظار المعلومات',
      'on_hold': 'معلقة',
    };
    return statusNames[status] ?? status;
  }

  /// الحصول على اسم الأولوية للعرض
  String _getPriorityDisplayName(String priority) {
    const priorityNames = {
      'low': 'منخفضة',
      'medium': 'متوسطة',
      'Medium': 'متوسطة',
      'high': 'عالية',
      'urgent': 'عاجلة',
    };
    return priorityNames[priority] ?? priority;
  }



  /// الحصول على لون الحالة
  Color _getStatusColor(String status) {
    const statusColors = {
      'pending': Colors.orange,
      'in_progress': Colors.blue,
      'completed': Colors.green,
      'cancelled': Colors.red,
      'waiting_for_info': Colors.purple,
      'on_hold': Colors.grey,
    };
    return statusColors[status] ?? Colors.grey;
  }

  /// الحصول على لون الأولوية
  Color _getPriorityColor(String priority) {
    const priorityColors = {
      'low': Colors.green,
      'medium': Colors.orange,
      'Medium': Colors.orange,
      'high': Colors.red,
      'urgent': Colors.purple,
    };
    return priorityColors[priority] ?? Colors.grey;
  }

  /// جلب إحصائيات المساهمين في المهام
  Future<Map<String, dynamic>> getTaskContributors({Map<String, dynamic>? filters}) async {
    try {
      final uri = Uri.parse('$_baseUrl/api/Dashboard/task-contributors');
      final response = await http.get(
        uri,
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data;
      } else {
        throw Exception('فشل في جلب إحصائيات المساهمين: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('خطأ في جلب إحصائيات المساهمين: $e');
      return {};
    }
  }

  /// جلب مصفوفة المستخدم والحالة المفصلة
  Future<Map<String, dynamic>> getUserTaskMatrix({Map<String, dynamic>? filters}) async {
    try {
      final uri = Uri.parse('$_baseUrl/api/Dashboard/user-task-matrix');
      final response = await http.get(
        uri,
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return data;
      } else {
        throw Exception('فشل في جلب مصفوفة المستخدم والحالة: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('خطأ في جلب مصفوفة المستخدم والحالة: $e');
      return {};
    }
  }

  /// جلب بيانات المساهمين حسب الحالة
  Future<List<ChartDataPoint>> getContributorsByStatus({Map<String, dynamic>? filters}) async {
    try {
      final data = await getTaskContributors(filters: filters);
      final contributorStats = data['contributorStats'] as List<dynamic>? ?? [];

      return _groupContributorsByStatus(contributorStats);
    } catch (e) {
      debugPrint('خطأ في جلب المساهمين حسب الحالة: $e');
      return [];
    }
  }

  /// جلب بيانات نشاط المستخدمين (عدد المساهمات لكل مستخدم)
  Future<List<ChartDataPoint>> getTasksByContributorCount({Map<String, dynamic>? filters}) async {
    try {
      final data = await getTaskContributors(filters: filters);
      final contributorStats = data['contributorStats'] as List<dynamic>? ?? [];

      // تحويل البيانات لعرض المستخدمين ومساهماتهم
      final userContributions = contributorStats.map((item) {
        final userName = item['userName'] as String? ?? 'مستخدم غير محدد';
        final totalContributions = item['totalContributions'] as int? ?? 0;

        return ChartDataPoint(
          label: userName,
          value: totalContributions.toDouble(),
          category: 'user_activity',
          color: DashboardConfig.getColorForValue('contributor', userName),
          metadata: {
            'userId': item['userId'],
            'userName': userName,
            'totalContributions': totalContributions,
            'completedTasks': item['completedTasks'] ?? 0,
            'pendingTasks': item['pendingTasks'] ?? 0,
            'inProgressTasks': item['inProgressTasks'] ?? 0,
            'cancelledTasks': item['cancelledTasks'] ?? 0,
            'waitingForInfoTasks': item['waitingForInfoTasks'] ?? 0,
          },
        );
      }).toList();

      // ترتيب المستخدمين حسب عدد المساهمات (تنازلي)
      userContributions.sort((a, b) => b.value.compareTo(a.value));

      // أخذ أول 20 مستخدم فقط لتجنب ازدحام المخطط
      return userContributions.take(20).toList();

    } catch (e) {
      debugPrint('خطأ في جلب نشاط المستخدمين: $e');
      return [];
    }
  }

  /// جلب بيانات المهام حسب نوع المهمة
  Future<List<ChartDataPoint>> getTasksByType({Map<String, dynamic>? filters}) async {
    try {
      final uri = Uri.parse('$_baseUrl/api/Tasks');
      final response = await http.get(
        uri,
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final tasks = (data['data'] as List?)?.map((item) => Task.fromJson(item)).toList() ?? [];

        return _groupTasksByType(tasks);
      } else {
        throw Exception('فشل في جلب المهام: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('خطأ في جلب المهام حسب النوع: $e');
      return [];
    }
  }

  /// جلب بيانات مصفوفة المستخدم والحالة للمساهمين
  Future<List<ChartDataPoint>> getContributorMatrix({Map<String, dynamic>? filters}) async {
    try {
      final data = await getUserTaskMatrix(filters: filters);
      final contributorMatrix = data['contributorMatrix'] as List<dynamic>? ?? [];

      return _convertMatrixToChartData(contributorMatrix, 'contributor');
    } catch (e) {
      debugPrint('خطأ في جلب مصفوفة المساهمين: $e');
      return [];
    }
  }

  /// جلب بيانات مصفوفة المستخدم والحالة للمكلفين
  Future<List<ChartDataPoint>> getAssigneeMatrix({Map<String, dynamic>? filters}) async {
    try {
      final data = await getUserTaskMatrix(filters: filters);
      final assigneeMatrix = data['assigneeMatrix'] as List<dynamic>? ?? [];

      return _convertMatrixToChartData(assigneeMatrix, 'assignee');
    } catch (e) {
      debugPrint('خطأ في جلب مصفوفة المكلفين: $e');
      return [];
    }
  }

  /// تجميع المساهمين حسب الحالة
  List<ChartDataPoint> _groupContributorsByStatus(List<dynamic> contributorStats) {
    final Map<String, double> statusCounts = {};

    for (final contributor in contributorStats) {
      statusCounts['مكتملة'] = (statusCounts['مكتملة'] ?? 0) + (contributor['completedTasks'] ?? 0).toDouble();
      statusCounts['قيد التنفيذ'] = (statusCounts['قيد التنفيذ'] ?? 0) + (contributor['inProgressTasks'] ?? 0).toDouble();
      statusCounts['في الانتظار'] = (statusCounts['في الانتظار'] ?? 0) + (contributor['pendingTasks'] ?? 0).toDouble();
      statusCounts['ملغية'] = (statusCounts['ملغية'] ?? 0) + (contributor['cancelledTasks'] ?? 0).toDouble();
      statusCounts['في انتظار المعلومات'] = (statusCounts['في انتظار المعلومات'] ?? 0) + (contributor['waitingForInfoTasks'] ?? 0).toDouble();
    }

    return statusCounts.entries.map((entry) => ChartDataPoint(
      label: entry.key,
      value: entry.value,
      color: DashboardConfig.getColorForValue('status', entry.key),
    )).toList();
  }

  /// تحويل بيانات المصفوفة إلى نقاط مخطط
  List<ChartDataPoint> _convertMatrixToChartData(List<dynamic> matrixData, String type) {
    final Map<String, double> userCounts = {};

    for (final item in matrixData) {
      final userName = item['userName'] ?? 'غير محدد';
      userCounts[userName] = (userCounts[userName] ?? 0) + (item['count'] ?? 0).toDouble();
    }

    return userCounts.entries.map((entry) => ChartDataPoint(
      label: entry.key,
      value: entry.value,
      color: DashboardConfig.getColorForValue(type, entry.key),
    )).toList();
  }


}
